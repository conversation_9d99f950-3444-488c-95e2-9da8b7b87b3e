<template>
  <div class="wheel-switching-page" @mouseenter="isHovered = true" @mouseleave="isHovered = false" ref="wheelContainer">
    <!-- 默认小白圆 -->
    <div class="default-circle" :class="{ 'fade-out': isHovered }">
      <div class="center-circle-bg"></div>
    </div>

    <!-- 完整轮盘 (悬停时显示) -->
    <div class="full-wheel" :class="{ 'fade-in': isHovered }">
      <!-- 白色透明背景 -->
      <div class="wheel-backdrop"></div>

      <!-- 轮盘背景 -->
      <div class="wheel-background">
      <!-- 使用conic-gradient创建扇形 -->
      <div class="wheel-sectors-bg" :style="{ background: dynamicBackground }"></div>

      <!-- 扇形内容 -->
      <div
        v-for="(sector, index) in sectors"
        :key="index"
        class="sector-content"
        :class="{
          active: currentSectorIndex === index,
          disabled: ![0, 1, 2].includes(index)
        }"
        :style="{
          transform: `translate(-50%, -50%) rotate(${sector.angle}deg)`,
          '--sector-color': sector.color,
          '--rotation-angle': `${sector.angle}deg`,
          ...calculateSectorPosition(sector.angle)
        }"
        @click.stop="handleSectorClick(sector)"
      >
        <div
          class="content-wrapper"
          :style="{
            transform: `rotate(${-sector.angle}deg)`
          }"
        >
          <div class="sector-icon">
            <img :src="sector.icon" />
          </div>
          <div class="sector-text">{{ sector.name }}</div>
        </div>
      </div>
    </div>

      <!-- 中心圆形 -->
      <div class="center-circle" @click.stop="handleWheelClick">
        <!-- 中心扇形背景 -->
        <div class="center-sector-bg" :style="{ background: calculateCenterSectorBackground() }"></div>
      </div>

      <!-- 中心指针 - 动态旋转 -->
      <div 
        class="center-pointer" 
        @click.stop="handleWheelClick"
        :style="{ transform: `translate(-50%, -50%) rotate(${pointerRotation}deg)` }"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import emitter from '@/utils/mitt'
import { useNavigationStore } from '@/stores/navigation'
import moyuIcon from '@/assets/icons/moyu.svg'
import officeIcon from '@/assets/icons/office.svg'
import jjIcon from '@/assets/icons/jj.svg'

// 使用 navigation store
const navigationStore = useNavigationStore()

// 6个扇形配置（标准60度等分，按照手绘图布局）
const sectors = [
  { id: 'entertainment', name: '摸鱼', angle: 240, color: '#ff6b6b', index: 0, icon: moyuIcon }, // 位置1：左上（210-270度区域）
  { id: 'office', name: '办公', angle: 180, color: '#4ecdc4', index: 1, icon: officeIcon },        // 位置2：左边（150-210度区域）
  { id: 'pure', name: '极简', angle: 120, color: '#45b7d1', index: 2, icon: jjIcon },             // 位置3：左下（90-150度区域）
  { id: 'entertainment', name: '摸鱼', angle: 60, color: '#ff6b6b', index: 3, icon: moyuIcon },   // 位置4：右下（30-90度区域）
  { id: 'office', name: '办公', angle: 0, color: '#4ecdc4', index: 4, icon: officeIcon },          // 位置5：右边（330-30度区域）
  { id: 'pure', name: '极简', angle: 300, color: '#45b7d1', index: 5, icon: jjIcon }              // 位置6：右上（270-330度区域）
]

// 响应式状态
const currentSectorIndex = ref(0)  // 默认选中摸鱼1（索引0，娱乐模式，240度位置，左上角）
const pointerRotation = ref(60)   // 初始指针方向（60度旋转，指向左上角的索引0）
const cumulativeRotation = ref(60)  // 累积旋转角度，用于确保始终顺时针旋转
const isRotating = ref(false)
const wheelContainer = ref(null)
const isHovered = ref(false)

// 获取当前模式
const getCurrentMode = () => sectors[currentSectorIndex.value].id

// 计算扇形内容的动态位置
const calculateSectorPosition = (angle) => {
  // 轮盘中心位置 (50%, 50%)
  const centerX = 50
  const centerY = 50
  // 目标径向距离 (从中心到内容的距离，以百分比表示)
  const radius = 37

  // 将角度转换为弧度
  const radian = (angle * Math.PI) / 180

  // 计算极坐标转笛卡尔坐标
  const x = centerX + radius * Math.cos(radian)
  const y = centerY + radius * Math.sin(radian)

  return {
    top: `${y}%`,
    left: `${x}%`
  }
}

// 计算指针指向的扇形索引
const calculatePointedSector = computed(() => {
  // 直接返回当前选中的扇形索引
  return currentSectorIndex.value
})

// 计算中心扇形背景 - 根据当前选中的扇形调整
const calculateCenterSectorBackground = () => {
  // 根据当前选中的扇形指针角度设置对应的高亮扇形
  // 使用标准60度等分：0-60, 60-120, 120-180, 180-240, 240-300, 300-360
  let startAngle, endAngle;

  switch(currentSectorIndex.value) {
    case 0: // 摸鱼1（左上角），高亮300-360度范围
      startAngle = 300;
      endAngle = 360;
      break;
    case 1: // 办公2（左边），高亮240-300度范围
      startAngle = 240;
      endAngle = 300;
      break;
    case 2: // 极简3（左下角），高亮180-240度范围
      startAngle = 180;
      endAngle = 240;
      break;
    case 3: // 摸鱼4（右下角），高亮120-180度范围
      startAngle = 120;
      endAngle = 180;
      break;
    case 4: // 办公5（右边），高亮60-120度范围
      startAngle = 60;
      endAngle = 120;
      break;
    case 5: // 极简6（右上角），高亮0-60度范围
      startAngle = 0;
      endAngle = 60;
      break;
    default:
      startAngle = 300;
      endAngle = 360;
  }
  
  // 创建60度的扇形高亮，与指针方向一致
  // 使用标准60度等分，无跨0度情况
  return `conic-gradient(
    from 0deg,
    transparent 0deg,
    transparent ${startAngle}deg,
    rgba(255, 255, 255, 0.9) ${startAngle}deg,
    rgba(255, 255, 255, 0.9) ${endAngle}deg,
    transparent ${endAngle}deg,
    transparent 360deg
  )`
}

// 动态背景计算
const dynamicBackground = computed(() => {
  const pointedIndex = calculatePointedSector.value
  const selectedColor = '#71C6FF'
  const transparentColor = 'transparent'

  // 使用标准60度等分：0-60, 60-120, 120-180, 180-240, 240-300, 300-360
  // 摸鱼1（左上角）→ 300-360度区域
  // 极简6（右上角）→ 0-60度区域
  // 办公5（右边）→ 60-120度区域
  // 摸鱼4（右下角）→ 120-180度区域
  // 极简3（左下角）→ 180-240度区域
  // 办公2（左边）→ 240-300度区域
  return `conic-gradient(
    from 0deg,
    ${pointedIndex === 5 ? selectedColor : transparentColor} 0deg 60deg,    /* 扇形5（极简6，右上）*/
    ${pointedIndex === 4 ? selectedColor : transparentColor} 60deg 120deg,  /* 扇形4（办公5，右边）*/
    ${pointedIndex === 3 ? selectedColor : transparentColor} 120deg 180deg, /* 扇形3（摸鱼4，右下）*/
    ${pointedIndex === 2 ? selectedColor : transparentColor} 180deg 240deg, /* 扇形2（极简3，左下）*/
    ${pointedIndex === 1 ? selectedColor : transparentColor} 240deg 300deg, /* 扇形1（办公2，左边）*/
    ${pointedIndex === 0 ? selectedColor : transparentColor} 300deg 360deg  /* 扇形0（摸鱼1，左上）*/
  )`
})

// 更新指针旋转角度 - 优化为最短路径旋转
function updatePointerRotation(sectorIndex) {
  // 只处理左边3个扇形的角度
  let targetAngle
  switch(sectorIndex) {
    case 0: // 摸鱼1（左上），指针需要旋转60度
      targetAngle = 60
      break
    case 1: // 办公2（左边），指针需要旋转0度
      targetAngle = 0
      break
    case 2: // 极简3（左下），指针需要旋转300度
      targetAngle = 300
      break
    default:
      targetAngle = 60
  }

  // 获取当前角度（标准化到0-360范围）
  const currentAngle = cumulativeRotation.value % 360

  // 计算最短路径的角度差
  let angleDiff = targetAngle - currentAngle

  // 优化：选择最短旋转路径
  if (angleDiff > 180) {
    angleDiff -= 360  // 逆时针更短
  } else if (angleDiff < -180) {
    angleDiff += 360  // 顺时针更短
  }

  // 特殊处理：从极简3（300度）到摸鱼1（60度）的情况
  if (currentAngle >= 270 && targetAngle <= 90) {
    // 从左下到左上，使用顺时针短路径：300→360→60 = 120度
    angleDiff = (360 - currentAngle) + targetAngle
  }
  // 特殊处理：从摸鱼1（60度）到极简3（300度）的情况
  else if (currentAngle <= 90 && targetAngle >= 270) {
    // 从左上到左下，使用逆时针短路径：60→0→300 = -120度
    angleDiff = -(currentAngle + (360 - targetAngle))
  }

  // 更新累积角度
  cumulativeRotation.value += angleDiff

  // 设置显示角度
  pointerRotation.value = cumulativeRotation.value
}

// 初始化当前扇形索引
function initCurrentSector() {
  // 强制默认指向摸鱼1（索引0，左上角）
  // 确保轮盘始终从摸鱼1开始显示
  currentSectorIndex.value = 0

  // 初始化累积角度为摸鱼1的目标角度（60度）
  const initialAngle = 60  // 摸鱼1（索引0）对应60度旋转
  cumulativeRotation.value = initialAngle
  pointerRotation.value = initialAngle
}

// 顺序切换到下一个扇形
function switchToNextSector() {
  if (isRotating.value) return

  // 只在左边3个扇形中循环：0（摸鱼1）→ 1（办公2）→ 2（极简3）→ 0
  const leftSectorIndices = [0, 1, 2]
  const currentIndexInLeft = leftSectorIndices.indexOf(currentSectorIndex.value)
  const nextIndexInLeft = (currentIndexInLeft + 1) % 3
  const nextIndex = leftSectorIndices[nextIndexInLeft]
  const nextSector = sectors[nextIndex]

  isRotating.value = true
  currentSectorIndex.value = nextIndex
  
  // 更新指针位置
  updatePointerRotation(nextIndex)

  // 发送模式切换事件
  setTimeout(() => {
    const currentMode = nextSector.id
    // 使用新的事件类型，直接传递目标模式
    emitter.emit('switchToMode', { targetMode: currentMode })
    emitter.emit('initdock')
    // 重置旋转状态
    setTimeout(() => {
      isRotating.value = false
    }, 100)
  }, 500) // 等待动画完成
}

// 扇形点击处理 - 直接跳转到指定模式
function handleSectorClick(sector) {
  if (isRotating.value) return

  // 只允许点击左边的3个扇形：索引0、1、2
  const leftSectorIndices = [0, 1, 2]
  if (!leftSectorIndices.includes(sector.index)) {
    return // 忽略右边扇形的点击
  }

  const targetMode = sector.id
  const currentMode = getCurrentMode()

  // 如果点击的是当前模式，不执行任何操作
  if (targetMode === currentMode && sector.index === currentSectorIndex.value) return

  isRotating.value = true
  currentSectorIndex.value = sector.index
  
  // 更新指针位置
  updatePointerRotation(sector.index)

  // 发送模式切换事件
  setTimeout(() => {
    // 使用新的事件类型，直接传递目标模式
    emitter.emit('switchToMode', { targetMode: targetMode })
    emitter.emit('initdock')
    // 重置旋转状态
    setTimeout(() => {
      isRotating.value = false
    }, 100)
  }, 500) // 等待动画完成
}

// 轮盘中心点击处理 - 顺序切换
function handleWheelClick() {
  switchToNextSector()
}

// 监听外部模式变化
function handleExternalModeChange() {
  // 从 navigationStore 获取当前状态
  const currentDataSource = navigationStore.currentDataSource
  const isPureMode = localStorage.getItem('isPureMode') === 'true'

  let newMode = isPureMode ? 'pure' : currentDataSource
  const currentMode = getCurrentMode()

  if (newMode !== currentMode) {
    // 找到对应模式的扇形索引，但只在左边3个扇形中查找（索引0、1、2）
    const leftSectors = sectors.filter((_, index) => [0, 1, 2].includes(index))
    let sectorIndex = leftSectors.findIndex(s => s.id === newMode)

    // 如果在左边扇形中找到了，获取实际的索引
    if (sectorIndex >= 0) {
      const actualIndex = [0, 1, 2][sectorIndex]


      currentSectorIndex.value = actualIndex
      // 更新指针位置
      updatePointerRotation(actualIndex)
    } else {
      console.warn('🎯 轮盘未找到匹配的左边扇形:', newMode)
    }
  }
}

// 监听 navigationStore 的数据源变化
watch(() => navigationStore.currentDataSource, () => {
  handleExternalModeChange()
}, { immediate: false })

// 组件挂载
onMounted(() => {
  initCurrentSector()

  // 监听外部模式变化事件
  window.addEventListener('storage', handleExternalModeChange)
  emitter.on('mode-changed', handleExternalModeChange)
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('storage', handleExternalModeChange)
  emitter.off('mode-changed', handleExternalModeChange)
})
</script>

<style scoped lang='scss'>
.wheel-switching-page {
  width: 200px;
  height: 200px;
  position: fixed;
  top: 50%;
  right: -100px;
  transform: translateY(-50%);
  cursor: pointer;
  z-index: 1000;
  transition: transform 0.3s ease;

  // 默认小白圆
  .default-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    background: #FFFFFF87;
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(5px);
    z-index: 10;
    opacity: 1;
    padding: 10px;
    transition: all 0.3s ease-out;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #ffffff;
    .center-circle-bg{
      background: #FFFFFF;
      width: 100%;
      height: 100%;
      width: 27px;
      height: 27px;
      border-radius: 50%;
    }
    // 淡出动画
    &.fade-out {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8);
    }
  }

  // 完整轮盘容器
  .full-wheel {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.3s ease-out;
    z-index: 11;
    // 淡入动画
    &.fade-in {
      opacity: 1;
      transform: scale(1);
    }
  }

  // 白色透明边框
  .wheel-backdrop {
    position: absolute;
    top: -5px;
    left: -5px;
    width: calc(100% + 10px);
    height: calc(100% + 10px);
    border-radius: 50%;
    border: 5px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    z-index: -1;
    transition: all 0.3s ease;
  }

  // 轮盘背景
  .wheel-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  // 扇形背景
  .wheel-sectors-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    // 背景通过动态计算设置
  }

  // 扇形内容
  .sector-content {
    position: absolute;
    width: 60px;
    height: 60px;
    transform-origin: center center;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 5;
    transition: transform 0.2s ease;
    // top 和 left 通过 JavaScript 动态计算
    // transform 通过内联样式设置，包含 translate(-50%, -50%) 和 rotate

    // 所有扇形都可点击
    cursor: pointer;

    // 悬停效果
    &:hover:not(.disabled) {
      transform: translate(-50%, -50%) rotate(var(--rotation-angle)) scale(1.1) !important;
    }

    // 禁用状态（右边的扇形）
    &.disabled {
      opacity: 0.3;
      cursor: not-allowed;
      pointer-events: none;
    }

    .content-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transform-origin: center center;
      // 旋转角度和径向位置通过内联样式动态计算

      .sector-icon {
        font-size: 18px;
        transition: transform 0.3s ease;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        
        img {
          width: 24px;
          height: 24px;
        }
      }

      .sector-text {
        font-size: 10px;
        font-weight: 700;
        color: #fff;
        text-align: center;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        transition: all 0.3s ease;
      }
    }

    // 激活状态
    &.active {
      .content-wrapper {
        .sector-icon {
          transform: scale(1.3);
        }

        .sector-text {
          font-size: 11px;
          font-weight: 800;
        }
      }
    }
  }

  // 中心圆形
  .center-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 88px;
    height: 88px;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
    z-index: 15;
    border: 6px solid rgb(255, 255, 255);
    overflow: hidden; // 确保扇形背景不超出圆形边界
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: translate(-50%, -50%) scale(1.05);
    }

    // 中心扇形背景
    .center-sector-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      z-index: 1;
      // 背景通过动态计算设置
    }
  }

  // 中心指针 (可旋转)
  .center-pointer {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 27px;
    height: 27px;
    background: #ffffff;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 100;
    &:hover {
      transform: translate(-50%, -50%) scale(1.1);
    }

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: -45px;
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      border-right: 16px solid #ffffff;
      transform: translateY(-50%);
      filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.3));
    }
  }

  // 悬停效果 - 只在非淡出状态时生效
  &:hover {
    .default-circle:not(.fade-out) {
      transform: translate(-50%, -50%) scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    width: 150px;
    height: 150px;
    right: -75px;

    .wheel-backdrop {
      top: -8px;
      left: -8px;
      width: calc(100% + 16px);
      height: calc(100% + 16px);
      border-width: 8px;
    }

    .center-circle {
      width: 45px;
      height: 45px;
    }

    .center-pointer {
      width: 9px;
      height: 9px;

      &::before {
        left: -15px;
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
        border-right: 12px solid #333;
      }
    }

    .sector-content .content-wrapper {
      .sector-icon {
        font-size: 14px;
      }

      .sector-text {
        font-size: 8px;
      }
    }

    // 移动端悬停效果调整
    &:hover {
      .default-circle:not(.fade-out) {
        transform: translate(-50%, -50%) scale(1.08);
      }
    }
  }
}
</style>
