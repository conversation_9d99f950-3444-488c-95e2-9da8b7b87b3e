<template>
  <div class="wallpaper-modal-overlay" v-if="visible" @click.self="handleClose">
    <div class="wallpaper-modal" :class="{'wallpaper-modal-fullscreen': isFullscreen}">
      <div class="wallpaper-modal-header">
        <div class="wallpaper-modal-title">壁纸中心</div>
        <div class="wallpaper-modal-spacer"></div>
        <div class="wallpaper-modal-controls">
          <button class="control-btn close-btn" @click="handleClose" title="关闭">
            <img :src="closeSvg" alt="close" />
          </button>
        </div>
      </div>
      <div class="wallpaper-modal-content">
        <!-- 左侧导航栏 -->
        <div class="wallpaper-sidebar">
          <div class="sidebar-item" :class="{ active: activeCategory === 'recommended' }" @click="setActiveCategory('recommended')">
            <img v-if="activeCategory === 'recommended'" :src="oneActiveSvg" alt="one" />
            <img v-else :src="oneSvg" alt="one" />
            精选壁纸
          </div>
          <div class="sidebar-item" :class="{ active: activeCategory === 'personal' }" @click="setActiveCategory('personal')">
            <img v-if="activeCategory === 'personal'" :src="twoActiveSvg" alt="two" />
            <img v-else :src="twoSvg" alt="two" />
            个人壁纸库
          </div>
          <div class="sidebar-submenu">
            <div class="submenu-item" :class="{ active: activeCategory === 'personal' && activePersonalSubCategory === 'uploads' }" @click="setActivePersonalSubCategory('uploads')">
              我的上传
            </div>
            <div class="submenu-item" :class="{ active: activeCategory === 'personal' && activePersonalSubCategory === 'favorites' }" @click="setActivePersonalSubCategory('favorites')">
              我的收藏
            </div>
            <div class="submenu-item" :class="{ active: activeCategory === 'personal' && activePersonalSubCategory === 'downloads' }" @click="setActivePersonalSubCategory('downloads')">
              我的下载
            </div>
          </div>
          <div class="sidebar-item" :class="{ active: activeCategory === 'upload' }" @click="setActiveCategory('upload')">
            <img v-if="activeCategory === 'upload'" :src="threeActiveSvg" alt="three" />
            <img v-else :src="threeSvg" alt="three" />
            本地上传
          </div>
          <div class="sidebar-item" :class="{ active: activeCategory === 'userPublickShare' }" @click="setActiveCategory('userPublickShare')">
            <img v-if="activeCategory === 'userPublickShare'" :src="fourActiveSvg" alt="four" />
            <img v-else :src="fourSvg" alt="four" />
            用户共享库
          </div>

          <!-- 版权声明 -->
          <div class="copyright-notice">
            壁纸资源来源于网络，如有问题请联系删除，谢谢
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="wallpaper-main">
          <!-- 推荐壁纸 -->
          <div v-if="activeCategory === 'recommended'" class="wallpaper-content-section">
            <!-- 分类标签栏 -->
            <div class="category-tags" v-if="false">
              <div class="tag-item" :class="{ active: activeTag === 'landscape' }" @click="setActiveTag('landscape')">
                风景
              </div>
              <div class="tag-item" :class="{ active: activeTag === 'anime' }" @click="setActiveTag('anime')">
                动漫
              </div>
              <div class="tag-item" :class="{ active: activeTag === 'scifi' }" @click="setActiveTag('scifi')">
                科幻
              </div>
              <div class="tag-item" :class="{ active: activeTag === 'favorite' }" @click="setActiveTag('favorite')">
                收藏
              </div>
              <div class="tag-item" :class="{ active: activeTag === 'download' }" @click="setActiveTag('download')">
                下载
              </div>

              <!-- 刷新按钮 -->
              <button
                class="refresh-wallpapers-btn"
                @click="refreshWallpapers"
                :disabled="wallpaperStore.isLoadingWallpapers"
                title="刷新推荐壁纸"
              >
                <ReloadOutlined :style="{ fontSize: '16px' }" />
              </button>
            </div>

            <!-- 官方壁纸网格 -->
            <div class="wallpaper-grid" ref="wallpaperGridRef" @scroll="handleGridScroll">
              <!-- 加载状态 -->
              <div v-if="wallpaperStore.isLoadingWallpapers && !wallpaperStore.isLoadingMore" class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载推荐壁纸...</p>
              </div>

              <!-- 壁纸网格 -->
              <div
                v-for="(wallpaper, index) in wallpaperStore.officialWallpapers"
                :key="wallpaper.id"
                class="wallpaper-item"
                :class="{ active: wallpaper.url === wallpaperStore.currentBgUrl }"
                @click="selectWallpaper(index)"
              >
                <div class="wallpaper-image">
                  <LazyImage
                    :src="wallpaper.sortUrl"
                    :alt="wallpaper.name"
                    @load="handleImageLoad(index)"
                    @error="handleImageError(index)"
                  />
                  <div v-if="wallpaper.url === wallpaperStore.currentBgUrl" class="selected-indicator">
                    <CheckOutlined :style="{ fontSize: '24px' }" />
                  </div>
                </div>
                <div class="wallpaper-actions">
                  <button class="action-btn favorite-btn" @click.stop="toggleFavorite(wallpaper)" :class="{ active: wallpaper.isFavorite }">
                    <HeartFilled v-if="wallpaper.isFavorite" :style="{ fontSize: '16px', color: '#FF0707' }" />
                    <HeartOutlined v-else :style="{ fontSize: '16px' }" />
                  </button>
                  <button class="action-btn download-btn" @click.stop="downloadWallpaper(wallpaper)">
                    <DownloadOutlined :style="{ fontSize: '16px' }" />
                  </button>
                </div>
              </div>
              
              <!-- 加载更多状态 -->
              <div v-if="wallpaperStore.isLoadingMore" class="loading-more-container">
                <div class="loading-spinner"></div>
                <p>加载更多壁纸...</p>
              </div>
              
              <!-- 没有更多数据提示 -->
              <div v-if="!wallpaperStore.hasMoreWallpapers && wallpaperStore.officialWallpapers.length > 2" class="no-more-data">
                <p>已加载全部壁纸</p>
              </div>
            </div>
          </div>

          <!-- 用户共享壁纸库 -->
          <div v-else-if="activeCategory === 'userPublickShare'" class="wallpaper-content-section">
            <!-- 官方壁纸网格 -->
            <div class="wallpaper-grid" ref="wallpaperGridRef" @scroll="handleGridScroll">
              <!-- 加载状态 -->
              <div v-if="wallpaperStore.isLoadingWallpapers" class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载用户共享壁纸...</p>
              </div>

              <!-- 壁纸网格 -->
              <div
                v-for="(wallpaper, index) in wallpaperStore.userPublicWallpapers"
                :key="wallpaper.id"
                class="wallpaper-item"
                :class="{ active: wallpaper.url === wallpaperStore.currentBgUrl }"
                @click="selectWallpaper(index)"
              >
                <div class="wallpaper-image">
                  <LazyImage
                    :src="wallpaper.sortUrl"
                    :alt="wallpaper.name"
                    @load="handleImageLoad(index)"
                    @error="handleImageError(index)"
                  />
                  <div v-if="wallpaper.url === wallpaperStore.currentBgUrl" class="selected-indicator">
                    <CheckOutlined :style="{ fontSize: '24px' }" />
                  </div>
                </div>
                <div class="wallpaper-actions">
                  <button class="action-btn favorite-btn" @click.stop="toggleFavorite(wallpaper)" :class="{ active: wallpaper.isFavorite }">
                    <HeartFilled v-if="wallpaper.isFavorite" :style="{ fontSize: '16px', color: '#FF0707' }" />
                    <HeartOutlined v-else :style="{ fontSize: '16px' }" />
                  </button>
                  <button class="action-btn download-btn" @click.stop="downloadWallpaper(wallpaper)">
                    <DownloadOutlined :style="{ fontSize: '16px' }" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 个人壁纸库 -->
          <div v-else-if="activeCategory === 'personal'" class="wallpaper-content-section">
            <!-- 我的上传 -->
            <div v-if="activePersonalSubCategory === 'uploads'">
                <div v-if="wallpaperStore.isLoadingUserWallpapers && wallpaperStore.userWallpapers.length === 0" class="loading-container">
                    <div class="loading-spinner"></div>
                    <p>正在加载上传壁纸...</p>
                </div>
                <div v-else-if="wallpaperStore.userWallpapers.length === 0" class="empty-content">
                    <PictureOutlined :style="{ fontSize: '64px' }" />
                    <p>暂无上传壁纸，快去本地上传吧</p>
                </div>
                <div v-else class="wallpaper-grid" ref="userWallpaperGridRef">
                    <div v-for="(wallpaper, index) in wallpaperStore.userWallpapers" :key="wallpaper.id" class="wallpaper-item" :class="{ active: wallpaper.url === wallpaperStore.currentBgUrl }" @click="selectUserWallpaper(wallpaper)">
                        <div class="wallpaper-image">
                            <LazyImage :src="wallpaper.sortUrl || wallpaper.url" :alt="wallpaper.name" />
                            <div v-if="wallpaper.url === wallpaperStore.currentBgUrl" class="selected-indicator">
                                <CheckOutlined :style="{ fontSize: '24px' }" />
                            </div>
                        </div>
                        <div class="wallpaper-actions">
                          <button class="action-btn share-btn" @click.stop="shareWallpaper(wallpaper)">
                            <ShareAltOutlined :style="{ fontSize: '16px' }" />
                          </button>
                          <button class="action-btn download-btn" @click.stop="downloadWallpaper(wallpaper)">
                            <DownloadOutlined :style="{ fontSize: '16px' }" />
                          </button>
                          <button class="action-btn delete-btn" @click.stop="deleteWallpaper(wallpaper)">
                            <DeleteOutlined :style="{ fontSize: '16px' }" />
                          </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 我的收藏 -->
            <div v-if="activePersonalSubCategory === 'favorites'">
              <div v-if="wallpaperStore.isLoadingUserFavoriteWallpapers && wallpaperStore.userFavoriteWallpapers.length === 0" class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载收藏壁纸...</p>
              </div>
              <div v-else-if="wallpaperStore.userFavoriteWallpapers.length === 0" class="empty-content">
                <PictureOutlined :style="{ fontSize: '64px' }" />
                <p>暂无收藏壁纸，快去精选壁纸中收藏吧</p>
              </div>
              <div v-else class="wallpaper-grid" ref="userWallpaperGridRef">
                <div v-for="(wallpaper, index) in wallpaperStore.userFavoriteWallpapers" :key="wallpaper.id" class="wallpaper-item" :class="{ active: wallpaper.url === wallpaperStore.currentBgUrl }" @click="selectUserWallpaper(wallpaper)">
                  <div class="wallpaper-image">
                    <LazyImage :src="wallpaper.sortUrl || wallpaper.url" :alt="wallpaper.name" />
                    <div v-if="wallpaper.url === wallpaperStore.currentBgUrl" class="selected-indicator">
                      <CheckOutlined :style="{ fontSize: '24px' }" />
                    </div>
                  </div>
                  <div class="wallpaper-actions">
                    <button class="action-btn favorite-btn" @click.stop="toggleFavorite(wallpaper)" :class="{ active: wallpaper.isFavorite }">
                      <HeartFilled v-if="wallpaper.isFavorite" :style="{ fontSize: '16px', color: '#FF0707' }" />
                      <HeartOutlined v-else :style="{ fontSize: '16px' }" />
                    </button>
                    <button class="action-btn download-btn" @click.stop="downloadWallpaper(wallpaper)">
                      <DownloadOutlined :style="{ fontSize: '16px' }" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <!-- 我的下载 -->
            <div v-if="activePersonalSubCategory === 'downloads'">
              <div class="wallpaper-grid" ref="userDownloadedWallpaperGridRef">
                <div v-if="wallpaperStore.isLoadingWallpapers" class="loading-container">
                  <div class="loading-spinner"></div>
                  <p>正在加载下载壁纸...</p>
                </div>
                <div v-for="(wallpaper, index) in wallpaperStore.userDownloadedWallpapers" :key="wallpaper.id" class="wallpaper-item" :class="{ active: wallpaper.url === wallpaperStore.currentBgUrl }" @click="selectUserWallpaper(wallpaper)">
                  <div class="wallpaper-image">
                    <LazyImage :src="wallpaper.sortUrl || wallpaper.url" :alt="wallpaper.name" />
                    <div v-if="wallpaper.url === wallpaperStore.currentBgUrl" class="selected-indicator">
                      <CheckOutlined :style="{ fontSize: '24px' }" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 本地上传 -->
          <div v-else-if="activeCategory === 'upload'" class="wallpaper-content-section" style="align-items: flex-start;overflow: auto;">
            <div class="setting-group" style="width: 100%;">
              <h3>本地上传</h3>

              <div class="upload-container">
                <!-- 上传区域 -->
                <div 
                  class="upload-area" 
                  @click="triggerUpload" 
                  @dragover.prevent 
                  @drop.prevent="handleFileDrop"
                >
                  <input
                    type="file"
                    id="wallpaper-uploader"
                    accept="image/*"
                    @change="handleWallpaperUpload"
                    style="display: none;"
                  />
                  <div class="upload-icon">
                    <img :src="uploadCloudSvg" alt="upload" class="upload-cloud-icon" />
                  </div>
                  <p class="upload-text">点击或拖拽到此处上传</p>
                  <p class="upload-tip">支持jpg/png/bmp/webp/mp4</p>
                </div>

                <!-- 预览区域 -->
                <div v-if="wallpaperStore.userCustomWallpaper && !isLoggedIn" class="custom-wallpaper-preview">
                  <img :src="wallpaperStore.userCustomWallpaper" alt="自定义壁纸预览" />
                </div>
                
                <!-- 上传历史记录 -->
                <div class="upload-history" v-if="isLoggedIn && wallpaperStore.userWallpapers.length > 0">
                  <h3 class="history-title">上传历史</h3>
                  <div class="history-grid">
                    <div v-for="(item, index) in wallpaperStore.userWallpapers" :key="index" class="history-item" @click="selectUserWallpaper(index, true)">
                      <img :src="item.sortUrl || item.url" alt="历史壁纸" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onUnmounted, onMounted, nextTick } from 'vue';
import { useWallpaperStore } from '@/stores/wallpaper.js';
import { message, Tooltip as ATooltip, Modal } from 'ant-design-vue';
import oneSvg from '@/assets/paperIcon/one.svg'
import twoSvg from '@/assets/paperIcon/two.svg'
import threeSvg from '@/assets/paperIcon/three.svg'
import oneActiveSvg from '@/assets/paperIcon/oneActive.svg'
import twoActiveSvg from '@/assets/paperIcon/twoActive.svg'
import threeActiveSvg from '@/assets/paperIcon/threeActive.svg'
import fourSvg from '@/assets/paperIcon/four.svg'
import fourActiveSvg from '@/assets/paperIcon/fourActive.svg'


import {
  HeartOutlined,
  HeartFilled,
  DownloadOutlined,
  ReloadOutlined,
  UploadOutlined,
  CheckOutlined,
  PictureOutlined,
  ApiOutlined,
  ShareAltOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import closeSvg from '@/assets/modal/close.svg'
import LazyImage from '@/components/common/LazyImage.vue'
import uploadCloudSvg from '@/assets/modal/upload-cloud.svg'
import { useRuploadWallpaper, listUserWallpaper, userWallpaperIsShare, userDownloadWallpaper,
  userDeleteWallpaper, listUserCollectWallpaper, userCollectWallpaper
 } from '@/api/paper.js';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'update:visible']);

// 初始化wallpaper store
const wallpaperStore = useWallpaperStore();

const isFullscreen = ref(false);
const activeCategory = ref('recommended'); // 当前选中的主分类：recommended, personal, upload, settings
const activePersonalSubCategory = ref('uploads'); // 个人壁纸库下的子分类
const activeTag = ref('landscape'); // 当前选中的子分类标签

// 登录状态检查变量 - 实际项目中应该从用户认证store中获取
const isLoggedIn = ref(!!localStorage.getItem('token'));

// 懒加载统计
const loadedImagesCount = ref(0);
const failedImagesCount = ref(0);

// 滚动加载相关
const wallpaperGridRef = ref(null); // 壁纸网格的引用
const scrollThreshold = 100; // 距离底部多少像素时触发加载
let scrollDebounceTimer = null; // 防抖定时器

const userWallpaperGridRef = ref(null);


// 监听visible变化，重置全屏状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 重置懒加载统计
    loadedImagesCount.value = 0;
    failedImagesCount.value = 0;

    // 弹窗打开时，刷新精选壁纸数据
    if (!wallpaperStore.wallpapersLastFetch ||
        (Date.now() - wallpaperStore.wallpapersLastFetch) > 60 * 60 * 1000) {
      wallpaperStore.fetchOfficialWallpapers();
    }
    // 检查登录状态并获取用户壁纸
    checkLoginStatus();
  } else {
    if (isFullscreen.value) {
      toggleFullscreen();
    }
  }
});

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// 在新窗口打开
const openInNewWindow = () => {
  // 壁纸弹窗的新窗口打开逻辑可以后续实现
  console.log('在新窗口打开壁纸中心');
};

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;

  // 触发全局事件，通知布局组件修改z-index
  window.dispatchEvent(new CustomEvent('wallpaper-modal-fullscreen', {
    detail: { isFullscreen: isFullscreen.value }
  }));

  // 添加或移除body类以禁用滚动
  if (isFullscreen.value) {
    document.body.classList.add('wallpaper-modal-fullscreen-active');
  } else {
    document.body.classList.remove('wallpaper-modal-fullscreen-active');
  }
};

// 设置活动分类
const setActiveCategory = (category) => {
  activeCategory.value = category;
  // 切换分类时重置标签
  if (category !== 'personal') {
    activePersonalSubCategory.value = null;
  }
  if (category === 'recommended') {
    activeTag.value = 'landscape';

    // 切换到推荐壁纸分类时，确保数据已加载
    nextTick(() => {
      if (wallpaperStore.officialWallpapers.length <= 2) { // 只有默认壁纸
        wallpaperStore.fetchOfficialWallpapers();
      }
    });
  } else if (category === 'personal') {
    // 检查登录状态
    if (!isLoggedIn.value) {
      message.warn('此功能需要登录后使用');
      return;
    }
    // 点击个人壁纸库时，默认选中并加载“我的上传”
    setActivePersonalSubCategory('uploads');
  } else if (category === 'upload') {
    if (isLoggedIn.value) {
      wallpaperStore.fetchUserWallpapers();
    }
  } else if (category === 'userPublickShare') {
    wallpaperStore.fetchUserPublicWallpapers();
  }
};

// 设置个人壁纸库子分类
const setActivePersonalSubCategory = (subCategory) => {
  // 检查登录状态
  if (!isLoggedIn.value) {
    message.warn('此功能需要登录后使用');
    return;
  }

  activeCategory.value = 'personal'; // 确保父分类被激活
  activePersonalSubCategory.value = subCategory;
  if (subCategory === 'uploads') {
    wallpaperStore.fetchUserWallpapers();
  } else if (subCategory === 'downloads') {
    wallpaperStore.fetchUserDownloadedWallpapers();
  } else if (subCategory === 'favorites') {
    wallpaperStore.fetchUserFavoriteWallpapers();
  }
};

// 设置活动标签
const setActiveTag = (tag) => {
  activeTag.value = tag;
};

// 选择壁纸
const selectWallpaper = (index) => {
  wallpaperStore.selectOfficialWallpaper(index);
  wallpaperStore.setWallpaperMode(2); // 设置为推荐壁纸模式
};

// 选择用户壁纸
const selectUserWallpaper = (wallpaper, isFromHistory = false) => {
  if (wallpaper && wallpaper.url) {
    wallpaperStore.setCustomWallpaper(wallpaper.url);
    wallpaperStore.setWallpaperMode(1); // 设置为自定义模式
    if (isFromHistory) {
      message.success('已选择历史壁纸');
    }
  }
};



// 处理网格滚动
const handleGridScroll = () => {
  // 防抖处理
  if (scrollDebounceTimer) {
    clearTimeout(scrollDebounceTimer);
  }
  
  scrollDebounceTimer = setTimeout(() => {
    if (!wallpaperGridRef.value) return;
    
    const { scrollTop, scrollHeight, clientHeight } = wallpaperGridRef.value;
    const scrollBottom = scrollHeight - scrollTop - clientHeight;
    
    // 当滚动到距离底部threshold距离时，加载更多数据
    if (scrollBottom < scrollThreshold && 
        wallpaperStore.hasMoreWallpapers && 
        !wallpaperStore.isLoadingMore &&
        !wallpaperStore.isLoadingWallpapers) {
      console.log('触发加载更多壁纸');
      wallpaperStore.loadMoreWallpapers();
    }
  }, 200); // 200ms防抖
};

// 处理文件上传
const handleWallpaperUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 检查文件大小 (2MB)
  if (file.size > 2 * 1024 * 1024) {
    message.error('文件大小不能超过2MB');
    return;
  }

  const reader = new FileReader();
  reader.onload = (e) => {
    const imageUrl = e.target.result;
    wallpaperStore.setCustomWallpaper(imageUrl);
    wallpaperStore.setWallpaperMode(1); // 设置为自定义模式
    
    // 如果已登录，额外调用上传接口保存到服务器
    if (isLoggedIn.value) {
      uploadToServer(file);
    } else {
      message.success('壁纸上传成功');
    }
  };
  reader.readAsDataURL(file);
};

// 处理拖拽上传
const handleFileDrop = (event) => {
  const file = event.dataTransfer.files;
  if (!file) return;

  // 检查文件大小 (2MB)
  if (file.size > 2 * 1024 * 1024) {
    message.error('文件大小不能超过2MB');
    return;
  }

  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    message.error('请选择图片文件');
    return;
  }

  const reader = new FileReader();
  reader.onload = (e) => {
    const imageUrl = e.target.result;
    wallpaperStore.setCustomWallpaper(imageUrl);
    wallpaperStore.setWallpaperMode(1); // 设置为自定义模式
    console.log(isLoggedIn.value,'isLoggedIn.value')
    // 如果已登录，额外调用上传接口保存到服务器
    if (isLoggedIn.value) {
      uploadToServer(file);
    } else {
      message.success('壁纸上传成功');
    }
  };
  reader.readAsDataURL(file);
};

// 上传到服务器的方法（暂时为框架）
const uploadToServer = async (file) => {
  try {
    message.loading('正在上传...', 0);
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await useRuploadWallpaper(formData);
    
    if (response.status === 200) {
      // 上传成功后，刷新用户壁纸列表
      wallpaperStore.fetchUserWallpapers();
      
      message.destroy();
      message.success('壁纸已保存到服务器');
    } else {
      message.error(response.message)
      throw new Error(response.message || '上传失败');
    }
    
  } catch (error) {
    message.destroy();
    message.error(error)
    console.error('上传壁纸失败:', error);
  }
};

// 触发文件上传
const triggerUpload = () => {
  document.getElementById('wallpaper-uploader').click();
};


// 刷新推荐壁纸
const refreshWallpapers = async () => {
  try {
    await wallpaperStore.fetchOfficialWallpapers(true); // 强制刷新
    message.success('推荐壁纸刷新成功');
  } catch (error) {
    message.error('刷新推荐壁纸失败');
  }
};

// 处理图片加载成功
const handleImageLoad = (index) => {
  loadedImagesCount.value++;
};

// 处理图片加载失败
const handleImageError = (index) => {
  failedImagesCount.value++;
};

// 切换收藏状态
const toggleFavorite = async (wallpaper) => {
  if (!isLoggedIn.value) {
    message.warn('请先登录再收藏壁纸');
    return;
  }

  const originalIsFavorite = wallpaper.isFavorite;
  wallpaper.isFavorite = !wallpaper.isFavorite;

  try {
    await userCollectWallpaper(wallpaper.code);
    if (wallpaper.isFavorite) {
      message.success('已添加到收藏');
    } else {
      message.success('已取消收藏');
    }
    wallpaperStore.fetchUserFavoriteWallpapers(); // 刷新收藏列表
  } catch (error) {
    wallpaper.isFavorite = originalIsFavorite; // 失败时回滚状态
    message.error(error.message);
    console.error('收藏操作失败:', error);
  }
};

// 下载壁纸
const downloadWallpaper = async (wallpaper) => {
  if (isLoggedIn.value) {
    try {
      await userDownloadWallpaper(wallpaper.code);
    } catch (error) {
      console.error('下载壁纸失败:', error);
      message.error('下载壁纸失败');
    }
  }

  try {
    message.loading('正在下载壁纸...', 0);

    // 使用正确的图片URL
    const imageUrl = wallpaper.url;

    // 获取图片数据
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error('下载失败');
    }

    // 转换为blob
    const blob = await response.blob();

    // 创建本地URL
    const blobUrl = URL.createObjectURL(blob);

    // 创建下载链接
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = `${wallpaper.name || 'wallpaper'}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理blob URL
    URL.revokeObjectURL(blobUrl);

    message.destroy();
    message.success('壁纸下载成功！');
    // 下载成功后，自动切换到下载列表
    setActivePersonalSubCategory('downloads');
  } catch (error) {
    message.destroy();
    message.error('下载失败，请重试');
    console.error('下载壁纸失败:', error);
  }
};

// 分享壁纸
const shareWallpaper = async (wallpaper) => {
  try {
    message.loading('正在分享...', 0);
    const response = await userWallpaperIsShare({ id: wallpaper.id, isShare: 2 });
    if (response.status === 200) {
      message.destroy();
      message.success('壁纸共享成功,需要审核等待！');
      // 可选: 更新UI或壁纸状态
      // fetchUserWallpapers();
    } else {
      throw new Error(response.message || '分享失败');
    }
  } catch (error) {
    message.destroy();
    message.error(error.toString());
    console.error('分享壁纸失败:', error);
  }
};

// 删除壁纸
const deleteWallpaper = (wallpaper) => {
  Modal.confirm({
    title: '删除壁纸',
    content: `确定要删除壁纸 "${wallpaper.name}" 吗？`,
    okText: '确认',
    cancelText: '取消',
    centered: true,
    onOk: async () => {
      try {
        message.loading('正在删除...', 0);
        const response = await userDeleteWallpaper(wallpaper.id);
        if (response.status === 200) {
          message.destroy();
          message.success('壁纸删除成功');
          // 刷新用户上传的壁纸列表
          wallpaperStore.fetchUserWallpapers();
        } else {
          throw new Error(response.message || '删除失败');
        }
      } catch (error) {
        message.destroy();
        message.error(error.toString());
        console.error('删除壁纸失败:', error);
      }
    },
    onCancel() {},
  });
};

// 组件挂载时初始化
onMounted(() => {
  // 如果是推荐壁纸分类，确保数据已加载
  if (activeCategory.value === 'recommended' && wallpaperStore.officialWallpapers.length <= 2) {
    wallpaperStore.fetchOfficialWallpapers();
  }
  // 检查登录状态 (此处需要替换为实际的登录检查逻辑)
  checkLoginStatus();
});

// 检查登录状态
const checkLoginStatus = () => {
  isLoggedIn.value = !!localStorage.getItem('token');
  
  if (isLoggedIn.value) {
    wallpaperStore.fetchUserWallpapers();
    wallpaperStore.fetchUserFavoriteWallpapers();
    wallpaperStore.fetchUserDownloadedWallpapers();
  } else {
    wallpaperStore.userWallpapers = [];
    wallpaperStore.userFavoriteWallpapers = [];
    wallpaperStore.userDownloadedWallpapers = [];
  }
};


// 组件卸载时清理
onUnmounted(() => {
  document.body.classList.remove('wallpaper-modal-fullscreen-active');
  
  // 清理防抖定时器
  if (scrollDebounceTimer) {
    clearTimeout(scrollDebounceTimer);
    scrollDebounceTimer = null;
  }
});
</script>

<style scoped lang="scss">
.wallpaper-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.wallpaper-modal {
  width: 90%;
  height: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 1200px;
  position: relative;
  z-index: 1001;
}

.wallpaper-modal-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
  z-index: 10000;
}

.wallpaper-modal-overlay:has(.wallpaper-modal-fullscreen) {
  background-color: #fff;
  backdrop-filter: none;
  z-index: 99999 !important;
}

.wallpaper-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 53px;
  -webkit-app-region: drag;
}

.wallpaper-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  flex: 1;
  user-select: none;
}

.wallpaper-modal-spacer {
  width: 60px;
}

.wallpaper-modal-controls {
  display: flex;
  gap: 15px;
  margin-left: 4px;
  -webkit-app-region: no-drag;
}

.control-btn {
  width: 12px;
  height: 12px;
  padding: 5px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  outline: none;
  border: none;
}

.control-btn img {
  width: 100%;
  height: 100%;
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.wallpaper-modal-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  background-color: #fff;
}

/* 左侧导航栏样式 */
.wallpaper-sidebar {
  width: 164px;
  background-color: #f5f5f7;
  border-right: 1px solid #eaeaea;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px;
}

/* 版权声明样式 */
.copyright-notice {
  margin-top: auto;
  padding: 8px 15px;
  font-size: 10px;
  color: #999;
  line-height: 1.4;
  text-align: left;
  word-wrap: break-word;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background-color: rgba(0, 0, 0, 0.02);
}

.sidebar-item {
  padding: 10px 15px;
  font-size: 14px;
  color: #2F2D2D;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  font-weight: 500;
  font-family: Alibaba PuHuiTi 2.0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px
}

.sidebar-item.active {
  border-radius: 10px;
  color: #ffffff;
  background-color: #71C6FF;
  font-weight: 600;
}

.sidebar-item.non-clickable {
  cursor: default;
}

.sidebar-item.non-clickable:hover {
  background-color: transparent;
}

.sidebar-item:hover:not(.active) {
  background-color: rgba(0, 0, 0, 0.05);
}

.sidebar-submenu {
  padding-left: 30px;
}

.submenu-item {
  padding: 8px 15px;
  font-size: 13px;
  cursor: pointer;
  color: #8F8F8F;
}

.submenu-item.active {
  color: #71C6FF;
}

/* 右侧内容区域样式 */
.wallpaper-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 15px;
}

.wallpaper-content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 分类标签栏样式 */
.category-tags {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(213, 213, 213, 0.93);
}

.tag-item {
  font-size: 12px;
  color: #8F8F8F;
  cursor: pointer;
  transition: all 0.2s;
  padding: 5px 10px;
  border-radius: 4px;
}

.tag-item.active {
  color: #333;
  font-weight: 500;
}

.tag-item:hover:not(.active) {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 刷新按钮样式 */
.refresh-wallpapers-btn {
  margin-left: auto;
  padding: 5px 10px;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  background-color: #f9f9f9;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.refresh-wallpapers-btn:hover:not(:disabled) {
  background-color: #71C6FF;
  color: white;
  border-color: #71C6FF;
}

.refresh-wallpapers-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-wallpapers-btn svg {
  animation: none;
}

.refresh-wallpapers-btn:disabled svg {
  animation: spin 1s linear infinite;
}

/* 壁纸网格样式 */
.wallpaper-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill,minmax(180px,1fr));
  grid-template-rows: 120px;
  grid-auto-flow: dense;
  row-gap: 8px;
  column-gap: 12px;
  overflow-y: auto;
  padding-right: 5px;
  position: relative;
  /* 优化滚动性能 */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  max-height: calc(100vh - 170px); /* 限制最大高度以确保可滚动 */
}

/* 加载状态样式 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(113, 198, 255, 0.3);
  border-radius: 50%;
  border-top-color: #71C6FF;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.wallpaper-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
  height: 120px;
}

.wallpaper-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.wallpaper-item.active {
  border: 2px solid #71C6FF;
  box-shadow: 0 0 0 2px rgba(113, 198, 255, 0.3);
}

.wallpaper-image {
  width: 100%;
  height: 100%;
  position: relative;
}

/* LazyImage 组件在壁纸网格中的样式调整 */
.wallpaper-image :deep(.lazy-image-container) {
  border-radius: 8px;
}

.wallpaper-image :deep(.lazy-image) {
  border-radius: 8px;
}

.wallpaper-image :deep(.skeleton-loader) {
  border-radius: 8px;
}

.wallpaper-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}


.wallpaper-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding: 8px;
  background: linear-gradient(to top, rgba(0,0,0,0.4), transparent);
  opacity: 0;
  transition: opacity 0.2s;
}

.wallpaper-item:hover .wallpaper-actions {
  opacity: 1;
}

.action-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  color: #fff;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}


@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}


/* 选中指示器 */
.selected-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #71C6FF;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 2;
}

/* 空内容提示 */
.empty-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  gap: 16px;
}

/* 加载更多状态样式 */
.loading-more-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  margin-top: 10px;
}

.loading-more-container .loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(113, 198, 255, 0.3);
  border-radius: 50%;
  border-top-color: #71C6FF;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

/* 没有更多数据提示 */
.no-more-data {
  grid-column: 1 / -1;
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

/* 设置部分样式 */
.settings-section {
  overflow-y: auto;
  padding-right: 10px;
}

.setting-group {
  margin-bottom: 24px;
}

.setting-group h3 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.wallpaper-mode-selector {
  margin-bottom: 20px;
}

.wallpaper-mode-options {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.wallpaper-mode-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  border-radius: 8px;
  border: 1px solid #eaeaea;
  cursor: pointer;
  transition: all 0.2s;
  gap: 8px;
  padding: 12px;
  background-color: #f9f9f9;
}

.wallpaper-mode-option:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.wallpaper-mode-option.active {
  border-color: #71C6FF;
  background-color: rgba(113, 198, 255, 0.1);
}

.wallpaper-mode-option svg {
  color: #666;
}

.wallpaper-mode-option.active svg {
  color: #71C6FF;
}

.wallpaper-mode-option span {
  font-size: 12px;
  color: #666;
}

.wallpaper-mode-option.active span {
  color: #333;
  font-weight: 500;
}

/* 输入框样式 */
.settings-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 12px;
}

/* 滑块样式 */
.slider {
  width: 100%;
  margin-bottom: 16px;
}

/* 复选框样式 */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}

/* 按钮样式 */
.action-button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  background-color: #71C6FF;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.action-button:hover {
  background-color: #5ab8ff;
}

.action-button.refresh {
  margin-top: 12px;
}

/* 模糊效果预览 */
.blur-preview-container {
  position: relative;
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
}

.blur-preview-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.blur-preview-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.blur-text {
  color: white;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 上传按钮样式 */
.custom-wallpaper-uploader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-tip {
  font-size: 12px;
  color: #999;
}

.upload-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 4px;
  background-color: #71C6FF;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.upload-button:hover {
  background-color: #5ab8ff;
}

/* 上传区域样式 */
.upload-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  margin: 0 auto;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 400px;
  height: 200px;
  padding: 20px;
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.upload-area:hover {
  border-color: #71C6FF;
  background-color: #f0f9ff;
}

.upload-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}

.upload-cloud-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.upload-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 10px;
}

.upload-tip {
  font-size: 14px;
  color: #999;
}

/* 上传历史记录样式 */
.upload-history {
  width: 100%;
  margin-top: 30px;
}

.history-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #333;
  text-align: left;
}

.history-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill,minmax(180px,1fr));
  grid-template-rows: 120px;
  grid-auto-flow: dense;
  row-gap: 8px;
  column-gap: 12px;
  overflow-y: auto;
  padding-right: 5px;
  position: relative;
  /* 优化滚动性能 */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  max-height: calc(100vh - 170px); /* 限制最大高度以确保可滚动 */
}

.history-item {
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.history-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.history-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 测试按钮样式 */
.test-login-button {
  background-color: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  margin-bottom: 15px;
  font-size: 12px;
  cursor: pointer;
}

.test-login-button:hover {
  background-color: #e0e0e0;
}

/* 登录状态相关样式已删除 */

.custom-wallpaper-preview {
  width: 100%;
  max-width: 300px;
  height: 180px;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 16px;
}

.custom-wallpaper-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>

<style>
/* 全局样式，确保全屏模式下禁用页面滚动 */
body.wallpaper-modal-fullscreen-active {
  overflow: hidden !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 全屏模式下的弹窗样式 */
.wallpaper-modal-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  z-index: 9999 !important;
}
</style>