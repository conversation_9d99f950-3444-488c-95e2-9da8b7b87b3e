<template>
  <!-- 设置抽屉 -->
  <Drawer
    :open="modelValue"
    @update:open="(value) => $emit('update:modelValue', value)"
    title="设置"
    placement="right"
    :width="520"
    :closable="true"
    :mask="true"
    :maskClosable="true"
    @close="closeModal"
    class="settings-drawer"
    :headerStyle="{display: 'none'}"
    :bodyStyle="{padding: 0}"
    :maskStyle="{background: 'none'}"
  >
    <div class="settings-layout">
      <!-- 左侧设置分类 -->
      <div class="settings-sidebar">
        <div class="top-setting">
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_4267_1090)">
          <path d="M16.4723 8.10646L13.8313 3.08742C13.662 2.76576 13.4046 2.49578 13.0873 2.30729C12.7701 2.11879 12.4055 2.01911 12.0336 2.01923H6.02104C5.26855 2.01923 4.57877 2.42538 4.23179 3.0732L1.53537 8.09225C1.38473 8.37245 1.30605 8.68388 1.30605 9C1.30605 9.31612 1.38473 9.62755 1.53537 9.90775L4.23179 14.9268C4.40239 15.2446 4.65938 15.5108 4.97484 15.6967C5.29029 15.8825 5.65214 15.9807 6.02104 15.9808H12.0336C12.7924 15.9808 13.4864 15.5685 13.8313 14.9126L16.4723 9.89252C16.6177 9.61622 16.6935 9.31032 16.6935 9C16.6935 8.68968 16.6177 8.38378 16.4723 8.10748V8.10646ZM14.9945 15.4903C14.7157 16.0202 14.2917 16.4649 13.7692 16.7754C13.2467 17.0859 12.6461 17.2501 12.0336 17.25H6.02104C5.41336 17.2499 4.81733 17.088 4.29772 16.7819C3.77811 16.4758 3.3548 16.0372 3.07379 15.5137L0.377368 10.4946C0.129464 10.0332 0 9.52048 0 9C0 8.47952 0.129464 7.96675 0.377368 7.50535L3.07379 2.48529C3.35494 1.96201 3.77832 1.52359 4.29791 1.21766C4.81751 0.911731 5.41347 0.749986 6.02104 0.75H12.0336C13.2826 0.75 14.4259 1.43031 14.9945 2.50966L17.6355 7.52972C18.1215 8.45372 18.1215 9.54729 17.6355 10.4703L14.9945 15.4903Z" fill="#535353"/>
          <path d="M9 12.75C8.50754 12.75 8.01991 12.653 7.56494 12.4645C7.10997 12.2761 6.69657 11.9999 6.34835 11.6517C6.00013 11.3034 5.72391 10.89 5.53545 10.4351C5.347 9.98009 5.25 9.49246 5.25 9C5.25 8.50754 5.347 8.01991 5.53545 7.56494C5.72391 7.10997 6.00013 6.69657 6.34835 6.34835C6.69657 6.00013 7.10997 5.72391 7.56494 5.53545C8.01991 5.347 8.50754 5.25 9 5.25C9.99456 5.25 10.9484 5.64509 11.6517 6.34835C12.3549 7.05161 12.75 8.00544 12.75 9C12.75 9.99456 12.3549 10.9484 11.6517 11.6517C10.9484 12.3549 9.99456 12.75 9 12.75ZM9 11.4569C9.32264 11.4569 9.64213 11.3933 9.94021 11.2699C10.2383 11.1464 10.5091 10.9654 10.7373 10.7373C10.9654 10.5091 11.1464 10.2383 11.2699 9.94021C11.3933 9.64213 11.4569 9.32264 11.4569 9C11.4569 8.67736 11.3933 8.35787 11.2699 8.05979C11.1464 7.7617 10.9654 7.49086 10.7373 7.26271C10.5091 7.03457 10.2383 6.85359 9.94021 6.73012C9.64213 6.60665 9.32264 6.5431 9 6.5431C8.34839 6.5431 7.72347 6.80195 7.26271 7.26271C6.80195 7.72347 6.5431 8.34839 6.5431 9C6.5431 9.65161 6.80195 10.2765 7.26271 10.7373C7.72347 11.198 8.34839 11.4569 9 11.4569Z" fill="#535353"/>
          </g>
          <defs>
          <clipPath id="clip0_4267_1090">
          <rect width="18" height="18" fill="white"/>
          </clipPath>
          </defs>
          </svg>
          <span class="fontSetting">设置</span>
        </div>
          
        <div
          v-for="(item, index) in settingTabs"
          :key="index"
          class="settings-nav-item"
          :class="{ active: activeSettingTab === item.value }"
          @click="activeSettingTab = item.value"
        >
          <div v-if="item.noIcon" :class="item.noIcon + ' settings-icon'+ ' noIcon'"></div>
          <img v-else-if="activeSettingTab === item.value" :src="item.activeIcon" class="settings-icon" />
          <img v-else :src="item.icon" class="settings-icon" />
          <span>{{ item.name }}</span>
        </div>
      </div>

      <!-- 右侧设置内容 -->
      <div class="settings-content-area">
        <button v-if="isMobile" @click="closeModal" class="mobile-close-button">
          <img :src="closeSvg" />
        </button>
            <!-- 壁纸设置 -->
            <div v-if="activeSettingTab === 'wallpaper'" class="setting-panel">
              <div class="setting-group">
                <h3>当前壁纸</h3>
                <div class="blur-preview-container">
                  <img class="blur-preview-bg" :src="wallpaperStore.currentBgUrl" alt="背景预览">
                  <div class="blur-preview-effect" :style="{
                    backdropFilter: wallpaperStore.blurAmount > 0 ? `blur(${wallpaperStore.blurAmount}px)` : 'none',
                    WebkitBackdropFilter: wallpaperStore.blurAmount > 0 ? `blur(${wallpaperStore.blurAmount}px)` : 'none',
                    backgroundColor: wallpaperStore.bgTransparent ? 'rgba(0, 0, 0, 0)' : 'rgba(0, 0, 0, 0.3)'
                  }">
                    <div class="blur-text">效果预览</div>
                  </div>
                </div>
              </div>

              <div class="setting-group">
                <h3>背景模糊度 ({{ wallpaperStore.blurAmount }}px)</h3>
                <input
                  type="range"
                  v-model.number="wallpaperStore.blurAmount"
                  min="0"
                  max="20"
                  class="slider"
                  @input="wallpaperStore.setBlurAmount(wallpaperStore.blurAmount)"
                />
              </div>

              <div class="setting-group">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="wallpaperStore.bgTransparent" @change="wallpaperStore.toggleBgTransparent()" />
                  <span>移除背景蒙层</span>
                </label>
              </div>

              <div class="wallpaper-redirect-section">
                <button class="wallpaper-redirect-button" @click="openWallpaperModal">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <circle cx="8.5" cy="8.5" r="1.5"></circle>
                    <polyline points="21 15 16 10 5 21"></polyline>
                  </svg>
                  <span>打开壁纸中心</span>
                </button>
              </div>
            </div>
            
            <!-- 动画效果设置 -->
            <div v-if="activeSettingTab === 'animation'" class="setting-panel">
              <slot name="animation-settings"></slot>
            </div>

            <!-- 首页设置 -->
            <div v-if="activeSettingTab === 'home'" class="setting-panel">
              <div class="home-settings-container">
                <p class="setting-description">选择系统首次访问时的默认模式</p>
                
                <div class="home-mode-options">
                  <div 
                    class="home-mode-option" 
                    :class="{ active: homeMode === 'fun' }"
                    @click="setHomeMode('fun')"
                  >
                    <div class="i-carbon:game-console home-mode-icon"></div>
                    <h3>摸鱼模式</h3>
                    <p>首次访问时跳转到主页面</p>
                  </div>
                  
                  <div 
                    class="home-mode-option" 
                    :class="{ active: homeMode === 'office' }"
                    @click="setHomeMode('office')"
                  >
                    <div class="i-carbon:document home-mode-icon"></div>
                    <h3>办公模式</h3>
                    <p>首次访问时跳转到办公页面</p>
                  </div>
                </div>
                
              </div>
            </div>

            <!-- 布局设置 -->
            <div v-if="activeSettingTab === 'layout'" class="setting-panel">
              <SettingPage />
            </div>

            <!-- 系统设置 -->
            <div v-if="activeSettingTab === 'settings'" class="setting-panel">
              <SystemPage />
            </div>

            <!-- 时间设置 -->
            <div v-if="activeSettingTab === 'time'" class="setting-panel">
              <slot name="time-settings"></slot>
            </div>

            <!-- 备份还原 -->
            <div v-if="activeSettingTab === 'backup'" class="setting-panel">
              <h2 class="setting-title">备份与还原</h2>
              <div class="backup-container">
                <div class="backup-section">
                  <div class="backup-info">
                    <div class="info-icon i-carbon:information"></div>
                    <p>系统备份将保存您的当前设置和数据到服务器，便于后续恢复或迁移。</p>
                  </div>
                  
                  <div class="backup-options">
                    <div class="backup-option">
                      <div class="option-header">
                        <div class="i-carbon:template option-icon"></div>
                        <h4>备份布局、数据</h4>
                      </div>
                      <p>仅保存布局、位置和分类设置</p>
                      <button class="backup-button layout-backup" @click="handleLayoutBackup" :disabled="isBackingUp">
                        <span class="i-carbon:cloud-upload"></span>
                        <span>{{ isBackingUp ? '备份中...' : '创建布局备份' }}</span>
                      </button>
                    </div>
                  </div>
                </div>
                
                <div class="backup-divider"></div>
                
                <div class="backup-section">
                  <h3 class="backup-subtitle">系统还原</h3>
                  <div class="backup-warning">
                    <div class="warning-icon i-carbon:warning-alt"></div>
                    <p>警告：还原操作会覆盖当前系统数据，此操作不可撤销。请确认您要恢复的备份版本。</p>
                  </div>
                  
                  <div class="backup-options restore-options">
                    <div class="backup-option">
                      <div class="option-header">
                        <div class="i-carbon:document-import option-icon"></div>
                        <h4>恢复布局、数据</h4>
                      </div>
                      <p>从服务器恢复最新的完整备份（包含所有数据）</p>
                      <button class="backup-button restore-backup" @click="handleRestoreFullBackup" :disabled="isRestoring || backupList.length === 0">
                        <span class="i-carbon:restart"></span>
                        <span>{{ isRestoring ? '恢复中...' : '恢复最新备份' }}</span>
                      </button>
                    </div>
                  </div>

                  <!-- 备份历史列表 -->
                  <div class="backup-history">
                    <div class="backup-history-header">
                      <h4>备份历史</h4>
                      <button class="refresh-button" @click="fetchBackupList(true)" :disabled="isLoadingBackups">
                        <span class="i-carbon:refresh" :class="{ 'rotating': isLoadingBackups }"></span>
                        <span>{{ isLoadingBackups ? '加载中...' : '刷新' }}</span>
                      </button>
                    </div>

                    <div v-if="isLoadingBackups" class="backup-loading">
                      <div class="loading-spinner"></div>
                      <p>正在加载备份列表...</p>
                    </div>

                    <div v-else-if="backupList.length === 0" class="backup-empty">
                      <div class="i-carbon:document-blank empty-icon"></div>
                      <p>暂无备份记录</p>
                      <p class="empty-tip">创建第一个备份来保护您的数据</p>
                    </div>

                    <div v-else class="backup-list">
                      <div
                        v-for="backup in backupList"
                        :key="backup.id || backup.index"
                        class="backup-item"
                        :class="{ 'selected': selectedBackup?.id === backup.id }"
                      >
                        <div class="backup-item-info">
                          <div class="backup-item-header">
                            <span class="backup-index">#{{ backup.index }}</span>
                            <span class="backup-date">{{ backup.formattedDate }}</span>
                          </div>
                          <div class="backup-item-details">
                            <span class="backup-size">{{ backup.size }}</span>
                          </div>
                        </div>
                        <div class="backup-item-actions">
                          <button
                            class="backup-action-button restore-button"
                            @click="handleRestoreBackup(backup)"
                            :disabled="isRestoring"
                          >
                            <span class="i-carbon:restart"></span>
                            还原
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 公告日志 -->
            <div v-if="activeSettingTab === 'version'" class="setting-panel">
              <Version />
            </div>

            <div v-if="activeSettingTab === 'feedback'" class="setting-panel" style="height: 100%;">
              <div class="feedback-tabs">

                <div class="tab-header">
                  <div
                    v-for="item in iframeUrl"
                    :key="item.id"
                    class="tab-item"
                    :class="{ active: currentTab === item.id }"
                    @click="currentTab = item.id"
                  >
                    {{ item.name }}
                  </div>
                </div>

                <div class="tab-content">
                  <IframeCard
                    v-if="currentIframe"
                    :key="currentIframe.id"
                    :url="currentIframe.helpDocUrl"
                    :app-id="currentIframe.id"
                  />
                </div>
              </div>
            </div>

            <!-- 合作交流 -->
            <div v-if="activeSettingTab === 'exchange'" class="setting-panel">
              <Exchange />
            </div>

            <!-- 关于我们 -->
            <div v-if="activeSettingTab === 'about'" class="setting-panel">
              <div class="about-content custom-about">
                <h2 class="about-title">关于我们</h2>
                <div class="about-social" style="color: #000;">
                  高效人，摸鱼魂！我们是一群爱玩又会放松的年轻人。我们想拥有自己的一片鱼塘，就吭哧吭哧建了这个有趣的摸鱼导航网站。鱼塘已备好，希望鱼友们能捞到自己喜欢的鱼儿。
                  <br>
                  欢迎鱼友们提出好玩的想法，提交好玩的网站或小组件资源，一起浪呀浪，顺手“捞”到你的摸鱼搭子。
                </div>
                <Button
                  @click="handleHelp"
                  type="primary"
                  class="search-btn"
                >
                  帮助
                </Button>
                <div class="about-privacy" style="width: 100%;">

                  <Collapse style="width: 100%;">
                  <CollapsePanel key="1" header="隐私政策">
                    <div class="privacy-policy">
                      <div class="policy-intro">
                        欢迎访问我们的网站（以下简称"本网站"）。我们深知隐私对您的重要性，因此我们制定了本隐私政策，以透明、清晰的方式说明我们如何收集、使用、存储和保护您的个人信息。请仔细阅读以下内容。
                      </div>

                      <div class="policy-section">
                        <h3>1. 我们是谁</h3>
                        <p>我们的站点地址是：https://linkfun.com</p>
                      </div>

                      <div class="policy-section">
                        <h3>2. 信息的收集与使用</h3>
                        
                        <div class="subsection">
                          <h4>评论</h4>
                          <p>当您在本网站留下评论时，我们会收集以下信息：</p>
                          <ul>
                            <li>评论表单中填写的内容（如姓名、电子邮箱、评论内容等）。</li>
                            <li>您的IP地址和浏览器User-Agent字符串，用于识别和防止垃圾评论。</li>
                            <li>如果您使用Gravatar服务，您的电子邮箱地址的哈希值可能会被提供给Gravatar以确认是否关联了头像。</li>
                            <li>评论获批准后，您的头像和评论内容将公开展示。</li>
                          </ul>
                        </div>

                        <div class="subsection">
                          <h4>媒体</h4>
                          <p>如果您上传图片到本网站，请注意避免包含嵌入的地理位置信息（如EXIF GPS数据），其他访客可能会下载并提取这些信息。</p>
                        </div>

                        <div class="subsection">
                          <h4>Cookies</h4>
                          <ul>
                            <li>功能性Cookies：若您选择在评论时保存姓名、邮箱等信息，我们会使用Cookies以便您下次评论时无需重复填写。这些Cookies保留一年。</li>
                            <li>临时Cookies：访问登录页时，我们会设置临时Cookie以确认您的浏览器是否支持Cookies，关闭浏览器后该Cookie将被删除。</li>
                            <li>登录Cookies：登录后，我们会设置Cookies保存登录信息和屏幕显示选项。登录Cookies保留两天，屏幕选项Cookies保留一年。若选择"记住我"，登录状态将保留两周。</li>
                            <li>编辑Cookies：发布或编辑文章时，我们会保存一个仅含文章ID的Cookie，保留一天。</li>
                          </ul>
                        </div>

                        <div class="subsection">
                          <h4>嵌入内容</h4>
                          <p>本网站可能包含其他网站的嵌入内容（如视频、图片等）。这些内容的行为与您直接访问其来源网站无异，对方可能会收集数据、使用Cookies或跟踪您的交互行为。</p>
                        </div>
                      </div>

                      <div class="policy-section">
                        <h3>3. 信息的共享与披露</h3>
                        <ul>
                          <li>垃圾评论监测：评论可能会通过第三方服务进行垃圾内容检查。</li>
                          <li>法律要求：在法律法规允许或要求的情况下，我们可能会披露您的信息。</li>
                        </ul>
                      </div>

                      <div class="policy-section">
                        <h3>4. 信息的保留期限</h3>
                        <ul>
                          <li>评论：评论及其元数据将被无限期保存，以便自动识别并批准后续评论。</li>
                          <li>用户数据：注册用户可随时查看、编辑或删除个人信息（用户名除外）。管理员亦可访问和编辑这些信息。</li>
                        </ul>
                      </div>

                      <div class="policy-section">
                        <h3>5. 您的权利</h3>
                        <p>您有权：</p>
                        <ul>
                          <li>获取我们持有的您的个人数据副本。</li>
                          <li>要求删除您的个人数据（法律或管理必需的数据除外）。</li>
                          <li>更新或更正您的个人信息。</li>
                        </ul>
                        <p>如需行使上述权利，请联系我们。</p>
                      </div>

                      <div class="policy-section">
                        <h3>6. 数据的传输与安全</h3>
                        <p>您的数据可能通过第三方服务进行处理（如垃圾评论检测）。我们会采取合理措施保护您的信息，但无法完全保证互联网传输的绝对安全。</p>
                      </div>

                      <div class="policy-section">
                        <h3>7. 隐私政策的更新</h3>
                        <p>我们可能不定期更新本政策。更新后的版本将发布于此页面，请定期查阅。</p>
                      </div>

                      <div class="policy-section">
                        <h3>8. 联系我们</h3>
                        <p>如有任何疑问或请求，请通过以下方式联系我们：</p>
                        <ul>
                          <li>联系方式：<EMAIL></li>
                        </ul>
                      </div>
                    </div>
                  </CollapsePanel>
                  <CollapsePanel key="2" header="免责声明">
                   <div class="disclaimer-content">
                    <div class="disclaimer-header">
                      <p>欢迎使用 LinkFun妙趣导航（以下简称"本网站"）。在访问或使用本网站前，请您务必仔细阅读并充分理解本免责声明的全部内容。若您继续使用本网站，即表示您完全同意本声明的所有条款；如有异议，请立即停止使用本网站，并与我们联系。</p>
                    </div>

                    <div class="disclaimer-section">
                      <h3>1. 用户行为规范</h3>
                      <div class="subsection">
                        <p>1.1 您在使用本网站（包括但不限于访问、浏览、转载、宣传等）时，须遵守以下原则：</p>
                        <ul>
                          <li>符合中国法律法规、国际公约及社会公德。</li>
                          <li>不得利用本网站从事非法或不当用途。</li>
                          <li>不得干扰、破坏本网站及相关服务的正常运行。</li>
                          <li>遵守本网站的服务协议及相关规定。</li>
                        </ul>
                        <p>1.2 若因您的行为导致本网站或第三方权益受损，您需承担全部法律责任。</p>
                      </div>
                    </div>

                    <div class="disclaimer-section">
                      <h3>2. 网站内容免责</h3>
                      <div class="subsection">
                        <p>2.1 本网站收录的链接及内容来源于网络或用户提交，其版权归属原作者所有。若存在侵权或违法违规内容，请及时联系我们，我们将在核实后第一时间删除。</p>
                        <p>2.2 本网站中的广告或推广内容由第三方提供，其真实性、合法性由广告主负责。用户需自行判断并承担风险，本网站不承担任何责任。</p>
                        <p>2.3 本网站所发布的内容仅代表作者观点，与本网站立场无关，相关责任由作者自行承担。</p>
                      </div>
                    </div>

                    <div class="disclaimer-section">
                      <h3>3. 知识产权保护</h3>
                      <div class="subsection">
                        <p>3.1 未经本网站及原作者书面许可，任何机构或个人不得擅自复制、转载、链接或以其他方式使用本网站内容。</p>
                        <p>3.2 若您认为本网站内容侵犯了您的知识产权，请提供相关证明并联系我们，我们将依法处理。</p>
                      </div>
                    </div>

                    <div class="disclaimer-section">
                      <h3>4. 外部链接责任</h3>
                      <div class="subsection">
                        <p>本网站以链接形式推荐的其他网站，其内容、产品或服务由第三方提供。本网站不对其真实性、合法性负责，也不对因使用此类链接内容造成的损失承担任何责任。</p>
                      </div>
                    </div>

                    <div class="disclaimer-section">
                      <h3>5. 用户隐私与数据安全</h3>
                      <div class="subsection">
                        <p>5.1 您注册时提供的个人信息，本网站将严格保密，未经您同意不会向第三方泄露。</p>
                        <p>5.2 在以下情况下，本网站可能依法披露您的信息：</p>
                        <ul>
                          <li>应政府部门、司法机关的合法要求。</li>
                          <li>为维护公共安全或本网站合法权益。</li>
                        </ul>
                        <p>5.3 因您泄露密码或与他人共享账户导致的个人信息泄露，本网站不承担责任。</p>
                      </div>
                    </div>

                    <div class="disclaimer-section">
                      <h3>6. 免责范围</h3>
                      <div class="subsection">
                        <p>6.1 因不可抗力（如网络故障、黑客攻击等）导致的服务中断或数据丢失，本网站不承担责任。</p>
                        <p>6.2 本网站不保证服务的持续性、无错误性或完全安全性，用户需自行承担使用风险。</p>
                      </div>
                    </div>

                    <div class="disclaimer-section">
                      <h3>7. 争议解决与声明修改</h3>
                      <div class="subsection">
                        <p>7.1 若因本声明产生争议，双方应友好协商；协商不成，可依法向本网站所在地法院提起诉讼。</p>
                        <p>7.2 本网站有权根据需要修改本声明，修改后的内容将及时公布，恕不另行通知。</p>
                      </div>
                    </div>

                    <div class="disclaimer-section">
                      <h3>8. 联系我们</h3>
                      <div class="subsection">
                        <p>如对本声明有任何疑问或建议，请通过以下方式联系我们：</p>
                        <ul>
                          <li>联系方式：<EMAIL></li>
                        </ul>
                      </div>
                    </div>

                    <div class="disclaimer-footer">
                      <p>声明生效日期：2025年6月20日</p>
                      <p>最终解释权归：LinkFun妙趣导航所有</p>
                    </div>
                  </div>
                  </CollapsePanel>
                  <CollapsePanel key="3" header="公司介绍">
                   <div class="disclaimer-content">
                    公司介绍：
                    重庆市妙汇思科技文化有限公司，专注于办公提效相关的实用桌面工具、在线网页工具等产品的设计和开发，致力于为广大办公人群提高工作效率和提升工作体验，用热情和智慧竭力打造好用的、富有温度的的优质产品。
                    <br>联系地址：重庆市渝北区光电园双子座A栋2309室
                    <br>联系电话：13212366273
                  </div>
                  </CollapsePanel>
                  </Collapse>

                  <div>备案信息：渝ICP备2025057928号-1</div>
                </div>
                <div class="about-qrcodes">
                  <div class="qrcode-item">
                    <img :src="qqUrl" alt="QQ群" />
                    <div class="qrcode-label">{{ qqname }}</div>
                  </div>
                  <div class="qrcode-item">
                    <img :src="wxUrl" alt="微信群" />
                    <div class="qrcode-label">{{ wxname }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 重置系统 -->
            <div v-if="activeSettingTab === 'reset'" class="setting-panel">
              <h2 class="setting-title">重置系统</h2>
              <div class="reset-container">
                <div class="reset-warning">
                  <div class="i-carbon:warning-alt warning-icon"></div>
                  <p>警告：重置系统将清除所有本地存储的数据，包括自定义应用、布局和设置。此操作不可逆！</p>
                </div>
                
                <div class="reset-options">
                  <div class="reset-option">
                    <h3>完全重置</h3>
                    <p>清除所有本地存储的数据，包括应用列表、布局和设置，将系统恢复到初始状态。</p>
                    <button class="reset-button full-reset" @click="confirmFullReset">完全重置系统</button>
                  </div>


                  <div class="reset-option">
                    <h3>重置布局</h3>
                    <p>重置布局，不保留登录之前的数据，仅保留登陆之后的数据。</p>
                    <button class="reset-button user-data-reset" @click="confirmLoadUserData">重置布局</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
    </div>
  </Drawer>
</template>

<script setup>
import { ref, watch, onMounted, computed, onUnmounted } from 'vue'
import { Modal, message, Drawer, Collapse , CollapsePanel, Button } from 'ant-design-vue'
import { getAboutUs, backup, getBackupList } from '../../api/navbar'
import { useDeviceDetector } from '@/composables/useDeviceDetector'

import { useNavigationStore } from '@/stores/navigation'
import { useWallpaperStore } from '@/stores/wallpaper.js';
import autoBackupManager from '../../utils/autoBackupManager'
import Version from '@/components/settingCompemts/version.vue'
import emitter from '@/utils/mitt';
import Exchange from '@/components/settingCompemts/exchange.vue'
import SettingPage from '@/components/settingCompemts/settingPage.vue'
import SystemPage from '@/components/settingCompemts/SystemPage.vue'

import timeSvg from '@/assets/setting/time.svg'
import timeActiveSvg from '@/assets/setting/timeActive.svg'
import themeSvg from '@/assets/setting/theme.svg'
import themeActiveSvg from '@/assets/setting/themeActive.svg'

import settingsSvg from '@/assets/setting/setting.svg'
import settingsActiveSvg from '@/assets/setting/settingActive.svg'
import exchangeSvg from '@/assets/setting/exchange.svg'
import exchangeActiveSvg from '@/assets/setting/exchangeActive.svg'

import aboutSvg from '@/assets/setting/about.svg'
import aboutSvgActive from '@/assets/setting/aboutActive.svg'

import reportSvg from '@/assets/setting/report.svg'
import reportActiveSvg from '@/assets/setting/reportActive.svg'

import layout from '@/assets/setting/layout.svg'
import layoutActive from '@/assets/setting/layoutActive.svg'
import backups from '@/assets/setting/backup.svg'
import backupActive from '@/assets/setting/backupActive.svg'
import reset from '@/assets/setting/reset.svg'
import resetActive from '@/assets/setting/resetActive.svg'
import feedback from '@/assets/setting/feedback.svg'
import feedbackActive from '@/assets/setting/feedbackActive.svg'
import  closeSvg  from '@/assets/modal/close.svg'


import IframeCard from '@/components/iframe/IframeCard.vue'

// 接收外部传入的值
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// 触发事件
const emit = defineEmits(['update:modelValue', 'fullscreen-changed'])

const { isMobile, initDeviceDetector, cleanupDeviceDetector } = useDeviceDetector()

// 弹窗全屏状态
const isFullscreen = ref(false)
const wallpaperStore = useWallpaperStore();

// 当前激活的设置标签页
const activeSettingTab = ref('time')

// 社交媒体选中状态
const selectedSocial = ref('qq')

// 首页模式设置
const homeMode = ref('fun') // 默认为摸鱼模式

// 备份文件相关状态
const isBackingUp = ref(false);
const isRestoring = ref(false);
const lastFullBackupTime = ref('');
const lastLayoutBackupTime = ref('');

// 备份列表相关状态
const backupList = ref([]);
const isLoadingBackups = ref(false);

// Navigation Store
const navigationStore = useNavigationStore();
const selectedBackup = ref(null);
let fetchBackupTimer = null; // 防抖定时器

// 初始化首页模式
const initHomeMode = () => {
  const savedHomeMode = localStorage.getItem('homeMode')
  if (savedHomeMode) {
    homeMode.value = savedHomeMode
  }
}

// 设置首页模式
const setHomeMode = (mode) => {
  homeMode.value = mode
  localStorage.setItem('homeMode', mode)
  
  // 显示设置成功提示
  Modal.success({
    title: '设置成功',
    centered:true,
    content: mode === 'fun' ? '已设置为摸鱼模式，首次访问将跳转到主页面' : '已设置为办公模式，首次访问将跳转到办公页面'
  })
}

// 设置标签页配置
const settingTabs = [
  { name: '时间设置', value: 'time', icon: timeSvg, activeIcon: timeActiveSvg },
  { name: '主题壁纸', value: 'wallpaper', icon: themeSvg, activeIcon: themeActiveSvg },
  { name: '布局设置', value: 'layout', icon: layout, activeIcon: layoutActive },
  { name: '备份还原', value: 'backup', icon: backups, activeIcon: backupActive },
  { name: '系统设置', value: 'settings', icon: settingsSvg, activeIcon: settingsActiveSvg },
  { name: '重置系统', value: 'reset', icon: reset, activeIcon: resetActive },
  { name: '反馈建议', value: 'feedback', icon: feedback, activeIcon: feedbackActive },
  { name: '日志公告', value: 'version', icon: reportSvg, activeIcon: reportActiveSvg },
  { name: '关于我们', value: 'about', icon: aboutSvg, activeIcon: aboutSvgActive },
  { name: '合作交流', value: 'exchange', icon: exchangeSvg, activeIcon: exchangeActiveSvg },
]

// 布局设置相关响应式变量
const gridGap = 30 // 必须与Home.vue一致
const gridItemWidth = 60
const minColumns = 4
const maxColumns = 16
const maxGridColumns = ref(Number(localStorage.getItem('maxGridColumns')) || 16)
const maxGridWidth = computed(() => maxGridColumns.value * gridItemWidth + (maxGridColumns.value - 1) * gridGap)

// 监听并持久化设置
watch(maxGridColumns, (val) => {
  localStorage.setItem('maxGridColumns', val)
  localStorage.setItem('maxGridWidth', maxGridWidth.value)
  window.dispatchEvent(new CustomEvent('layoutSettingChanged'))
})

// 确认完全重置
function confirmFullReset() {
  Modal.confirm({
    title: '确认完全重置系统',
    content: '此操作将清除所有本地存储的数据，包括自定义应用、布局和设置，系统将返回初始状态。此操作不可逆！',
    okText: '确认重置',
    okType: 'danger',
    cancelText: '取消',
    centered:true,
    onOk: async () => {
      // 执行完全重置
      // const token = localStorage.getItem('token')  
      // if(token) {
      //   await userRestBackup().then(res => {
      //     if(res.status == 200) {
      //       message.success('重置成功')
      //     }
      //   })
      // }
      await navigationStore.refreshAllApiData()
      performFullResetSouce();
      
    }
  });
}

const helpUrl = ref('')

// 帮助文档链接
function handleHelp() {
  if(!helpUrl.value) {
    message.error('暂无帮助文档')
    return
  }
  emitter.emit('open-model-url',{url: helpUrl.value, name: '帮助文档'})
  closeModal()
}

// 确认加载用户数据
function confirmLoadUserData() {
  // 检查用户是否已登录
  const token = localStorage.getItem('token')
  if (!token) {
    message.error('请先登录后再使用此功能')
    return
  }

  Modal.confirm({
    title: '确认加载用户数据',
    content: '此操作将清除当前所有数据并从服务器加载您的个人配置。这将包括您的应用列表、布局和设置。此操作不可逆！',
    okText: '确认加载',
    okType: 'danger',
    cancelText: '取消',
    centered: true,
    onOk: () => {
      // 执行用户数据加载
      performLoadUserData();
    }
  });
}

// 执行完全重置
function performFullReset() {
  // 清除所有localStorage数据
  localStorage.clear();
  
  // 显示重置成功提示
  Modal.success({
    title: '重置成功',
    content: '系统已完全重置，所有数据已清除。',
    centered:true,
    onOk: () => {
      // 刷新页面
      window.location.reload();
    }
  });
}


//重置所有数据但保留token根userinfo、tokenType

async function performFullResetSouce() {
  // 保留的键列表
  const keysToKeep = ['token', 'tokenType', 'userInfo'];

  // 获取所有localStorage键
  const allKeys = Object.keys(localStorage);

  // 清除所有非保留的键
  allKeys.forEach(key => {
    if (!keysToKeep.includes(key)) {
      localStorage.removeItem(key);
    }
  });
  // 显示重置成功提示
  Modal.success({
    title: '重置成功',
    content: '系统已完全重置，所有数据已清除。',
    centered:true,
    onOk: () => {
      // 刷新页面
      window.location.reload();
    }
  });
}


// 执行用户数据加载
async function performLoadUserData() {
  try {
    // 显示加载提示
    const loadingMessage = message.loading('正在加载用户数据...', 0)

    // 调用 Navigation Store 的重置为用户数据方法
    await navigationStore.resetToUserData()

    // 关闭加载提示
    loadingMessage()

    // 显示成功提示
    Modal.success({
      title: '用户数据加载成功',
      content: '已成功加载您的个人配置，页面将自动刷新以应用更改。',
      centered: true,
      onOk: () => {
        // 刷新页面
        window.location.reload();
      }
    });

  } catch (error) {
    console.error('加载用户数据失败:', error)

    // 显示错误提示
    Modal.error({
      title: '加载用户数据失败',
      content: `加载失败：${error.message || '未知错误'}。请检查网络连接或稍后重试。`,
      centered: true
    });
  }
}

// 关闭弹窗
function closeModal() {
  emit('update:modelValue', false)
  
  // 如果处于全屏状态，通知退出全屏
  if (isFullscreen.value) {
    isFullscreen.value = false
    emit('fullscreen-changed', false)
  }
}

// 切换弹窗全屏状态
function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value
  
  // 触发事件通知父组件
  emit('fullscreen-changed', isFullscreen.value)
  
  // 添加或移除body类以禁用滚动
  if (isFullscreen.value) {
    document.body.classList.add('modal-fullscreen-active')
  } else {
    document.body.classList.remove('modal-fullscreen-active')
  }
}

// 在组件卸载时确保清理
watch(
  () => props.modelValue,
  (newVal) => {
    if (!newVal && isFullscreen.value) {
      // 关闭弹窗时也清理全屏状态
      document.body.classList.remove('modal-fullscreen-active')
    }
  }
)
const qqUrl = ref('')
const wxUrl = ref('')
const qqname = ref('')
const wxname = ref('')
const iframeUrl = ref([])
// 当前激活的标签页ID
const currentTab = ref(null)

// 计算当前激活的iframe数据
const currentIframe = computed(() => {
  return iframeUrl.value.find(item => item.id === currentTab.value)
})

function getQQandWXImage() {
  getAboutUs().then(res => {
    
    if(res.status == 200) {
       qqUrl.value = res.data[0].url
       qqname.value = res.data[0].name
       wxUrl.value = res.data[1].url
       wxname.value = res.data[1].name
       helpUrl.value = res.data.find(item => item.name === '帮助文档链接').helpDocUrl
       res.data.map((item) => {
        if(item.type === 3) {
          iframeUrl.value.push(item)
        }
       })
       console.log(iframeUrl.value,'iframeUrl.value')

       // 设置默认激活的标签页（第一个标签）
       if (iframeUrl.value.length > 0 && !currentTab.value) {
         currentTab.value = iframeUrl.value[0].id
       }
    }
  })
 }

// 获取备份列表（带防抖）
function fetchBackupList(immediate = false) {
  const token = localStorage.getItem('token')
  if (!token) {
    console.log('用户未登录，跳过获取备份列表')
    return
  }

  // 如果不是立即执行，使用防抖
  if (!immediate) {
    if (fetchBackupTimer) {
      clearTimeout(fetchBackupTimer)
    }
    fetchBackupTimer = setTimeout(() => {
      performFetchBackupList()
    }, 300) // 300ms防抖
  } else {
    // 立即执行
    if (fetchBackupTimer) {
      clearTimeout(fetchBackupTimer)
      fetchBackupTimer = null
    }
    performFetchBackupList()
  }
}

// 实际执行获取备份列表的函数
async function performFetchBackupList() {
  try {
    isLoadingBackups.value = true
    const response = await getBackupList()

    if (response.status === 200 && response.data) {
      // 处理备份列表数据，添加格式化信息
      backupList.value = response.data.map((backup, index) => ({
        ...backup,
        index: index + 1,
        formattedDate: formatBackupDate(backup.createtime),
        size: calculateBackupSize(backup.data)
      }))
      console.log('获取备份列表成功:', backupList.value)
    } else {
      console.warn('获取备份列表失败:', response)
      backupList.value = []
    }
  } catch (error) {
    console.error('获取备份列表时发生错误:', error)
    backupList.value = []
  } finally {
    isLoadingBackups.value = false
  }
}

// 格式化备份日期
function formatBackupDate(dateString) {
  if (!dateString) return '未知时间'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '时间格式错误'
  }
}

// 计算备份大小
function calculateBackupSize(data) {
  if (!data) return '0 KB'
  try {
    const sizeInBytes = new Blob([JSON.stringify(data)]).size
    if (sizeInBytes < 1024) return `${sizeInBytes} B`
    if (sizeInBytes < 1024 * 1024) return `${(sizeInBytes / 1024).toFixed(1)} KB`
    return `${(sizeInBytes / (1024 * 1024)).toFixed(1)} MB`
  } catch (error) {
    return '未知大小'
  }
}

// 打开壁纸弹窗
function openWallpaperModal() {
  // 关闭设置弹窗
  emit('update:modelValue', false);
  // 使用mitt发射打开壁纸弹窗事件
  emitter.emit('open-wallpaper-modal');
}

// 组件挂载时初始化
onMounted(() => {
  initHomeMode()
  getQQandWXImage()
  fetchBackupList(true) // 立即执行
  initDeviceDetector()
})

onUnmounted(() => {
  cleanupDeviceDetector()
})

// 监听弹窗打开状态，打开时刷新备份列表
watch(() => props.modelValue, (newValue, oldValue) => {
  if (newValue && !oldValue) {
    // 弹窗从关闭变为打开时，刷新备份列表
    console.log('设置弹窗打开，刷新备份列表')
    fetchBackupList()
  }
})

// 监听设置标签切换，切换到备份标签时刷新
watch(() => activeSettingTab.value, (newTab, oldTab) => {
  if (newTab === 'backup' && oldTab !== 'backup' && props.modelValue) {
    // 切换到备份标签且弹窗是打开状态时，刷新备份列表
    console.log('切换到备份标签，刷新备份列表')
    fetchBackupList()
  }
})

// 处理布局备份
function handleLayoutBackup() {
  const token = localStorage.getItem('token')  
  if(!token) return message.error('请先登录')
  Modal.confirm({
    title: '确认创建布局备份',
    content: '将备份布局和分类数据到服务器',
    okText: '确认',
    cancelText: '取消',
    centered: true,
    onOk: () => {
      // 执行备份操作
      isBackingUp.value = true;

      try {
        // 备份指定的四个localStorage项
        const backupKeys = [
            'categoryApps_entertainment', 
            'homeDockApps', 
            'categoryApps_office', 
            'officeDockApps',
        ]
        const layoutData = {}

        backupKeys.forEach(key => {
          const data = localStorage.getItem(key)
          if (data) {
            try {
              // 验证是否为有效JSON并存储
              layoutData[key] = JSON.parse(data)
            } catch (error) {
              console.warn(`备份项 ${key} 不是有效的JSON格式:`, error)
              // 如果不是JSON格式，直接存储原始字符串
              layoutData[key] = data
            }
          }
        })

        const allLayoutInfo = JSON.stringify(layoutData)
        console.log('备份数据:', layoutData)

        backup(allLayoutInfo).then((res) => {
          if(res.status === 200) {
            isBackingUp.value = false;
            message.success('布局、数据备份已成功创建');
            // 备份成功后自动刷新备份列表
            fetchBackupList(true); // 立即执行
          }else{
            isBackingUp.value = false;
            message.error('备份失败，请稍后重试');
          }
        }).catch((error) => {
          isBackingUp.value = false;
          console.error('备份API调用失败:', error)
          message.error('备份失败，请稍后重试');
        })
      } catch (error) {
        isBackingUp.value = false;
        console.error('备份过程中发生错误:', error)
        message.error('备份失败，请稍后重试');
      }
    }
  });
}

// 恢复指定备份
function handleRestoreBackup(backup = null) {
  const token = localStorage.getItem('token')
  if(!token) return message.error('请先登录')

  // 如果没有指定备份，使用最新的备份
  const targetBackup = backup || (backupList.value.length > 0 ? backupList.value[0] : null)

  if (!targetBackup) {
    return message.error('没有可用的备份数据')
  }

  const backupDate = formatBackupDate(targetBackup.createtime)

  Modal.confirm({
    title: '确认恢复备份',
    content: `此操作将使用 ${backupDate} 的备份覆盖当前的所有数据和布局，且不可恢复。确认继续吗？`,
    okText: '确认恢复',
    okType: 'danger',
    cancelText: '取消',
    centered: true,
    onOk: () => {
      isRestoring.value = true

      try {
        // 解析备份数据
        const backupData = typeof targetBackup.data === 'string'
          ? JSON.parse(targetBackup.data)
          : targetBackup.data

        console.log('恢复数据:', backupData)
        
        // 恢复指定的四个localStorage项
        const restoreKeys = [
            'categoryApps_entertainment', 
            'homeDockApps', 
            'categoryApps_office', 
            'officeDockApps',
        ]

        let restoredCount = 0
        restoreKeys.forEach(key => {
          if (backupData[key] !== undefined) {
            try {
              // 如果备份数据是对象，转换为JSON字符串存储
              const dataToStore = typeof backupData[key] === 'object'
                ? JSON.stringify(backupData[key])
                : backupData[key]

              localStorage.setItem(key, dataToStore)
              console.log(`已恢复 ${key}`)
              restoredCount++
            } catch (error) {
              console.error(`恢复 ${key} 时发生错误:`, error)
            }
          } else {
            console.warn(`备份数据中未找到 ${key}`)
          }
        })

        isRestoring.value = false
        autoBackupManager.performBackup()
        if (restoredCount > 0) {
          message.success('还原成功');
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        } else {
          message.error('还原失败，备份数据中没有可恢复的内容');
        }
      } catch (error) {
        isRestoring.value = false
        console.error('解析备份数据失败:', error)
        message.error('还原失败，备份数据格式错误');
      }
    }
  });
}

// 兼容旧的函数名
function handleRestoreFullBackup() {
  handleRestoreBackup()
}


</script>

<style lang="scss" scoped>
/* 设置内容样式 */
.settings-layout {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
}

.settings-sidebar {
  width: 154px;
  background-color: #F2F2F2;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 5px;
  overflow-y: auto;
  flex-shrink: 0;
  align-items: center;
}

.settings-nav-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 134px;
  justify-content: center;
  &:hover {
    // background: rgba(0, 0, 0, 0.05);
  }

  &.active {
    color: white;
    background: #71C6FF;
    border-radius: 8px;
    // border-left: 3px solid var(--accent-color, #4285F4);

    .settings-icon {
      color: var(--accent-color, #4285F4);
    }

    span {
      color: #ffffff;
      font-weight: 500;
    }
  }
}

.settings-content-area {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background-color: #fff;
}

.settings-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #333;
  flex-shrink: 0;
}

.settings-nav-item span {
  color: #333;
  font-size: 12px;
  line-height: 1.2;
}

.setting-panel {
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  animation: fadeIn 0.3s ease;
  background-color: #fff;
  text-align: left;
}

.setting-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 标签页样式 */
.animation-tabs {
  display: flex;
  padding: 15px;
  gap: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;
  border: none;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.tab-button:hover {
  background-color: rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.tab-button.active {
  background-color: rgba(var(--accent-color-rgb, 66, 133, 244), 0.1);
  color: var(--accent-color, #4285F4);
  box-shadow: 0 4px 8px rgba(var(--accent-color-rgb, 66, 133, 244), 0.2);
}

.tab-icon {
  font-size: 1.2rem;
}

.tab-panel {
  animation: fadeIn 0.3s ease;
}

/* 动画预览容器 */
.animation-preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.animation-preview {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

#animation-demo-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.preview-label {
  font-size: 14px;
  color: #666;
}

/* 动画选项 */
.animation-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.animation-option {
  padding: 12px;
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.03);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  &:hover {
    background: rgba(0, 0, 0, 0.06);
    transform: translateY(-2px);
  }
  
  &.active {
    background: rgba(var(--accent-color-rgb, 66, 133, 244), 0.1);
    box-shadow: 0 4px 10px rgba(var(--accent-color-rgb, 66, 133, 244), 0.2);
  }
  
  .option-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .option-icon {
    margin-bottom: 8px;
    font-size: 1.5rem;
  }
  
  .option-name {
    font-size: 12px;
    color: #333;
  }
  
  .check-icon {
    color: var(--accent-color, #4285F4);
    font-size: 1.2rem;
  }
}

/* 卡片动画预览 */
.card-animation-preview {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.demo-card {
  width: 100px;
  height: 100px;
  background-color: rgba(var(--accent-color-rgb, 66, 133, 244), 0.1);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.demo-icon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.demo-text {
  font-size: 12px;
  color: #333;
}

/* 设置操作按钮 */
.setting-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.action-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &.apply {
    background: var(--accent-color, #4285F4);
    color: white;
  }
}

/* 动画效果定义 */
.animate-bounce {
  color: black;
  animation: bounce 1s ease infinite;
}

.animate-rotate {
  animation: rotate 1.5s linear infinite;
}

.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

.animate-shake {
  animation: shake 0.8s cubic-bezier(.36,.07,.19,.97) infinite;
}

.animate-swing {
  transform-origin: center top;
  animation: swing 1.5s ease infinite;
}

/* 动画关键帧 */
@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-15px); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-5px); }
  40%, 80% { transform: translateX(5px); }
}

@keyframes swing {
  0%, 100% { transform: rotate(0deg); }
  20% { transform: rotate(15deg); }
  40% { transform: rotate(-10deg); }
  60% { transform: rotate(5deg); }
  80% { transform: rotate(-5deg); }
}

/* 卡片动画效果 */
.card-animate-fade-up {
  animation: card-fade-up 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-fade-down {
  animation: card-fade-down 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-fade-left {
  animation: card-fade-left 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-fade-right {
  animation: card-fade-right 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-slide-in {
  animation: card-slide-in 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-zoom-in {
  animation: card-zoom-in 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-flip {
  animation: card-flip 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  backface-visibility: visible;
}

.card-animate-rotate {
  animation: card-rotate 0.7s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.card-animate-reveal {
  animation: card-reveal 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: bottom;
}

.card-animate-glitch {
  animation: card-glitch 0.8s linear;
}

.card-animate-pulse {
  animation: card-pulse 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes card-fade-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes card-fade-down {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes card-fade-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes card-fade-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes card-slide-in {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes card-zoom-in {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes card-flip {
  from {
    opacity: 0;
    transform: perspective(400px) rotateY(90deg);
  }
  to {
    opacity: 1;
    transform: perspective(400px) rotateY(0deg);
  }
}

@keyframes card-rotate {
  from {
    opacity: 0;
    transform: rotate(-180deg) scale(0.5);
  }
  to {
    opacity: 1;
    transform: rotate(0) scale(1);
  }
}

@keyframes card-reveal {
  from {
    opacity: 0;
    transform: scaleY(0);
  }
  to {
    opacity: 1;
    transform: scaleY(1);
  }
}

@keyframes card-glitch {
  0% {
    opacity: 0;
    transform: translate(0);
    clip-path: inset(100% 0 0 0);
  }
  20% {
    opacity: 1;
    transform: translate(-5px, 5px);
    clip-path: inset(10% 0 60% 0);
  }
  40% {
    transform: translate(5px, -5px);
    clip-path: inset(40% 0 30% 0);
  }
  60% {
    transform: translate(5px, 5px);
    clip-path: inset(10% 0 60% 0);
  }
  80% {
    transform: translate(-5px, -5px);
    clip-path: inset(20% 0 20% 0);
  }
  100% {
    transform: translate(0);
    opacity: 1;
    clip-path: inset(0);
  }
}

@keyframes card-pulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
    box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb, 66, 133, 244), 0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 0 0 15px rgba(var(--accent-color-rgb, 66, 133, 244), 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(var(--accent-color-rgb, 66, 133, 244), 0);
  }
}

/* 壁纸重定向部分样式 */
.wallpaper-redirect-section {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 20px;
}

.wallpaper-redirect-content {
  text-align: center;
  max-width: 400px;
}

.wallpaper-redirect-icon {
  margin-bottom: 24px;
  color: #71C6FF;
}

.wallpaper-redirect-content h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.wallpaper-redirect-content p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 32px;
}

.wallpaper-redirect-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #71C6FF;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.wallpaper-redirect-button:hover {
  background-color: #5ab8ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(113, 198, 255, 0.3);
}

/* 壁纸设置相关样式 */
.wallpaper-mode-selector {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.wallpaper-mode-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.wallpaper-mode-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 10px;
  width: 90px;
  border-radius: 8px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.02);
  
  &:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  &.active {
    border-color: var(--accent-color, #4285F4);
    background: rgba(var(--accent-color-rgb, 66, 133, 244), 0.1);
    box-shadow: 0 0 10px rgba(var(--accent-color-rgb, 66, 133, 244), 0.2);
  }
  
  [class^="i-carbon"] {
    font-size: 24px;
    color: var(--accent-color, #4285F4);
  }
  
  span {
    font-size: 12px;
    font-weight: 500;
    color: #333;
  }
}

.custom-wallpaper-uploader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-top: 10px;
}

.upload-button {
  background: var(--accent-color, #4285F4);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    background: var(--accent-color-dark, #2b5797);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .i-carbon\:cloud-upload {
    font-size: 18px;
  }
}

.custom-wallpaper-preview {
  width: 100%;
  max-width: 300px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  
  &::after {
    content: '当前自定义壁纸';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 5px;
    text-align: center;
    font-size: 12px;
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.official-wallpaper-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
  gap: 10px;
  margin-top: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.official-wallpaper-item {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
  
  &.active {
    box-shadow: 0 0 0 3px var(--accent-color, #4285F4);
  }
  
  img {
    width: 100%;
    height: 50px;
    object-fit: cover;
    display: block;
  }
}

.wallpaper-name {
  font-size: 12px;
  padding: 5px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  backdrop-filter: blur(2px);
}

.selected-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background: var(--accent-color, #4285F4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.blur-preview-container {
  width: 100%;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  margin-top: 10px;
}

.blur-preview-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.blur-preview-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.blur-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.settings-input {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  margin-bottom: 10px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: var(--accent-color, #4285F4);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb, 66, 133, 244), 0.2);
  }
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  outline: none;
  margin: 10px 0;
  
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color, #4285F4);
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  }
  
  &::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--accent-color, #4285F4);
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  }
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  
  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--accent-color, #4285F4);
  }
  
  span {
    font-size: 14px;
    color: #333;
  }
}

.setting-group {
  margin-bottom: 24px;
  h3{
    color: black;
  }
  label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
  }
}

/* 主题设置相关样式 */
.theme-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 15px;
  border-radius: 10px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  }
  
  &.active {
    border-color: var(--accent-color, #4285F4);
  }
  
  .theme-icon {
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 16px;
    color: #555;
  }
  
  .active-check {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background: var(--accent-color, #4285F4);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
  }
  
  span {
    font-size: 13px;
    color: #333;
    text-transform: capitalize;
  }
}

.theme-preview {
  width: 100%;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-ui-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 20px 1fr;
  grid-template-rows: 20px 1fr;
  gap: 2px;
  padding: 4px;
}

.preview-header {
  grid-column: 1 / 3;
  border-radius: 2px;
}

.preview-sidebar {
  grid-row: 2 / 3;
  border-radius: 2px;
}

.preview-content {
  border-radius: 2px;
}

/* 主题预览样式 */
.theme-preview.light .preview-header {
  background-color: #f8f9fa;
}
.theme-preview.light .preview-sidebar {
  background-color: #e9ecef;
}
.theme-preview.light .preview-content {
  background-color: #ffffff;
}

.theme-preview.dark .preview-header {
  background-color: #1f2937;
}
.theme-preview.dark .preview-sidebar {
  background-color: #111827;
}
.theme-preview.dark .preview-content {
  background-color: #374151;
}

.theme-preview.blue .preview-header {
  background-color: #93c5fd;
}
.theme-preview.blue .preview-sidebar {
  background-color: #3b82f6;
}
.theme-preview.blue .preview-content {
  background-color: #dbeafe;
}

.theme-preview.green .preview-header {
  background-color: #6ee7b7;
}
.theme-preview.green .preview-sidebar {
  background-color: #10b981;
}
.theme-preview.green .preview-content {
  background-color: #d1fae5;
}

.theme-preview.purple .preview-header {
  background-color: #c4b5fd;
}
.theme-preview.purple .preview-sidebar {
  background-color: #8b5cf6;
}
.theme-preview.purple .preview-content {
  background-color: #ede9fe;
}

/* 重置系统相关样式 */
.reset-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.reset-warning {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: rgba(255, 87, 87, 0.1);
  border-left: 4px solid #ff5757;
  border-radius: 6px;
  margin-bottom: 10px;
}

.warning-icon {
  font-size: 24px;
  color: #ff5757;
  flex-shrink: 0;
}

.reset-warning p {
  margin: 0;
  color: #333;
  font-size: 14px;
  line-height: 1.5;
}

.reset-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.reset-option {
  padding: 20px;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: rgba(0, 0, 0, 0.02);
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.reset-option h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 500;
}

.reset-option p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15px;
}

.reset-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  margin-top: auto;
}

.reset-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.reset-button:active {
  transform: translateY(0);
}

.full-reset {
  background-color: #ff5757;
  color: white;
}

.full-reset:hover {
  background-color: #ff3b3b;
  box-shadow: 0 4px 8px rgba(255, 87, 87, 0.3);
}

.layout-reset {
  background-color: #ffbd2e;
  color: white;
}

.layout-reset:hover {
  background-color: #ffb310;
  box-shadow: 0 4px 8px rgba(255, 189, 46, 0.3);
}

.user-data-reset {
  background-color: #4CAF50;
  color: white;
}

.user-data-reset:hover {
  background-color: #45a049;
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.theme-ui-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 首页设置样式 */
.home-settings-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.home-mode-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 10px;
}

.home-mode-option {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px;
  border-radius: 10px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  background-color: rgba(0, 0, 0, 0.02);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  &.active {
    border-color: var(--accent-color, #4285F4);
    background-color: rgba(var(--accent-color-rgb, 66, 133, 244), 0.05);
  }
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: #666;
  }
  
  .home-mode-icon {
    font-size: 30px;
    color: var(--accent-color, #4285F4);
    margin-bottom: 5px;
  }
}

.setting-info {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 12px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 8px;
  margin-top: 10px;
  
  .setting-info-icon {
    font-size: 20px;
    color: var(--accent-color, #4285F4);
    flex-shrink: 0;
  }
  
  p {
    margin: 0;
    font-size: 13px;
    color: #666;
    line-height: 1.5;
  }
}

/* 关于我们相关样式 */
.about-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
}

.about-title {
  font-size: 22px;
  font-weight: bold;
  color: #222;
}

.about-social {
  display: flex;
  gap: 32px;
  margin-bottom: 10px;
  .social-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 18px;
    border-radius: 16px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: border 0.2s, background 0.2s;
    img {
      width: 54px;
      height: 54px;
      margin-bottom: 6px;
    }
    span {
      font-size: 16px;
      color: #666;
    }
    &.active {
      border: 2px solid #1890ff;
      background: #e6f7ff;
      span {
        color: #1890ff;
      }
    }
  }
}

.about-privacy {
  margin: 18px 0 10px 0;
  font-size: 15px;
  color: #444;
  line-height: 1.8;
  text-align: left;
  > div:first-child {
    font-weight: bold;
    margin-bottom: 4px;
  }
}

.about-qrcodes {
  display: flex;
  gap: 32px;
  margin-top: 18px;
  justify-content: flex-start;
  .qrcode-item {
    background: #f7f7f7;
    border-radius: 16px;
    padding: 18px 18px 8px 18px;
    display: flex;
    flex-direction: column;
    align-items: center;
    img {
      width: 120px;
      height: 120px;
      margin-bottom: 8px;
    }
    .qrcode-label {
      font-size: 15px;
      color: #666;
      margin-top: 2px;
    }
  }
}

/* 备份还原相关样式 */
.backup-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.backup-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.backup-subtitle {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.backup-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.backup-option {
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: rgba(0, 0, 0, 0.02);
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.option-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.option-icon {
  font-size: 24px;
  color: var(--accent-color, #4285F4);
}

.backup-option h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.backup-option p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.backup-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.backup-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.backup-button:active:not(:disabled) {
  transform: translateY(0);
}

.backup-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.full-backup {
  background-color: var(--accent-color, #4285F4);
  color: white;
}

.layout-backup {
  background-color: #2ecc71;
  color: white;
}

.restore-backup {
  background-color: #f39c12;
  color: white;
  margin-top: 5px;
}

.backup-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 10px 0;
}

.backup-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 15px;
  border-left: 4px solid #3477f5;
  background-color: rgba(66, 133, 244, 0.05);
  border-radius: 8px;
}

.info-icon {
  font-size: 20px;
  color: var(--accent-color, #4285F4);
  flex-shrink: 0;
}

.backup-warning {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 15px;
  background-color: rgba(255, 152, 0, 0.05);
  border-left: 4px solid #ff9800;
  border-radius: 8px;
}

.warning-icon {
  font-size: 20px;
  color: #ff9800;
  flex-shrink: 0;
}

.backup-info p,
.backup-warning p {
  margin: 0;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.file-upload-container {
  position: relative;
  margin-top: 5px;
}

.file-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
}

.file-label {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.05);
  border: 1px dashed rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #333;
}

.file-label:hover {
  background-color: rgba(0, 0, 0, 0.08);
  border-color: var(--accent-color, #4285F4);
}

.selected-file {
  font-size: 13px;
  color: #666;
  margin-top: 8px;
}

.backup-tips {
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 10px;
  margin-top: 20px;
}

.backup-tips h4 {
  margin: 0 0 10px 0;
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.backup-tips ul {
  margin: 0;
  padding-left: 20px;
}

.backup-tips li {
  font-size: 13px;
  color: #555;
  margin-bottom: 5px;
  line-height: 1.4;
}

.backup-status {
  padding: 8px;
  margin: 5px 0;
  background-color: rgba(0, 0, 0, 0.02);
  font-size: 13px;
  color: #666;
  text-align: center;
}

.restore-layout {
  background-color: #3498db;
  color: white;
}

/* 备份历史相关样式 */
.backup-history {
  margin-top: 30px;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.backup-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.backup-history-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #f8f9fa;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  transition: all 0.2s ease;
}

.refresh-button:hover:not(:disabled) {
  background-color: #e9ecef;
  color: #333;
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.backup-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #666;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #4285F4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.backup-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.5;
}

.empty-tip {
  font-size: 12px;
  margin-top: 5px;
  opacity: 0.7;
}

.backup-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.backup-item:hover {
  border-color: #4285F4;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);
}

.backup-item.selected {
  border-color: #4285F4;
  background-color: rgba(66, 133, 244, 0.05);
}

.backup-item-info {
  flex: 1;
}

.backup-item-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 5px;
}

.backup-index {
  font-size: 12px;
  font-weight: 500;
  color: #4285F4;
  background-color: rgba(66, 133, 244, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.backup-date {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.backup-item-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.backup-size {
  font-size: 12px;
  color: #666;
}

.backup-item-actions {
  display: flex;
  gap: 8px;
}

.backup-action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.select-button {
  background-color: #f8f9fa;
  color: #666;
}

.select-button.active {
  background-color: #4285F4;
  color: white;
  border-color: #4285F4;
}

.select-button:hover:not(.active) {
  background-color: #e9ecef;
  color: #333;
}

.restore-button {
  background-color: #f39c12;
  color: white;
  border-color: #f39c12;
}

.restore-button:hover:not(:disabled) {
  background-color: #e67e22;
  border-color: #e67e22;
}

.restore-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ant-drawer-body{
  padding: 0px;
}
.coll-3 p {
  font-size: 12px;
}
.top-setting{
  display: flex;
  align-items: center;
  height: 50px;
  padding-left: 25px;
  width: 100%;
  .fontSetting{
    color: black;
    font-family: Alibaba PuHuiTi 2.0;
    font-weight: 600;
    font-style: 75 SemiBold;
    font-size: 18px;
    leading-trim: NONE;
    line-height: 100%;
    letter-spacing: 5%;
  }
}

// 反馈建议标签页样式
.feedback-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;

  .tab-header {
    display: flex;
    flex-wrap: nowrap;
    gap: 8px;
    padding: 16px 0;
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 16px;
    overflow-x: auto;

    .tab-item {
      flex-shrink: 0;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #666;
      background: #f5f5f5;
      border: 1px solid transparent;
      transition: all 0.3s ease;
      white-space: nowrap;

      &:hover {
        color: #1890ff;
        background: #e6f7ff;
        border-color: #91d5ff;
      }

      &.active {
        color: #fff;
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        transform: translateY(-1px);
      }
    }
  }

  .tab-content {
    flex: 1;
    overflow: hidden;
  }
}

.mobile-close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  z-index: 1000;
  background: none;
}
</style>
