import { createRouter, createWebHistory, createWebHashHistory} from 'vue-router'
import Home from '../views/Home.vue'
import Layout from '../components/layout/Layout.vue'
import { useWallpaperStore } from '../stores/wallpaper.js'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Layout',
    component: Layout,
    meta: { requiresAuth: false },
    children:[
      {
        path: '',  // 将子路由的path设为空字符串
        name: 'Home',
        component: Home,
        meta: { requiresAuth: false }
      }
    ]
  },
  {
    path: '/moyu',
    name: 'moyu',
    component: () => import('@/components/iframe/ImageCard.vue'),
  },
  {
    path: '/multi-iframe',
    name: 'MultiIframe',
    component: () => import('@/views/MultiIframePage.vue'),
    meta: { requiresAuth: false }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_APP_BUILD_PATH),
  routes,
  linkActiveClass: 'active'
})

// 添加全局路由守卫来记录上次访问的路由
router.beforeEach((to, from, next) => {
  console.log(to.path,'topath')
  // 处理路由记忆功能
  if (to.path === '/office') {
    // 获取 wallpaper store 实例
    const wallpaperStore = useWallpaperStore()
    // 记录这两个路径之间的跳转
    localStorage.setItem('currentDataSource', 'office');
    // 使用 store 方法来设置官方壁纸为索引 1（办公背景）
    wallpaperStore.selectOfficialWallpaper(1)
    console.log('已设置办公模式壁纸为索引 1')

    router.push('/')
  }else if(to.path === '/fun'){
    // 获取 wallpaper store 实例
    const wallpaperStore = useWallpaperStore()
    // 记录这两个路径之间的跳转
    localStorage.setItem('currentDataSource', 'entertainment');
    // 使用 store 方法来设置官方壁纸为索引 1（办公背景）
    console.log('已设置办公模式壁纸为索引 1')
    router.push('/')  
  }else if(to.path === '/lite'){
    // 获取 wallpaper store 实例
    const wallpaperStore = useWallpaperStore()
    // 记录这两个路径之间的跳转
    localStorage.setItem('currentDataSource', 'office');
    // 使用 store 方法来设置官方壁纸为索引 1（办公背景）
    wallpaperStore.selectOfficialWallpaper(1)
    // 设置标记，指示需要进入纯净模式（避免时序问题）
    localStorage.setItem('shouldEnterPureMode', 'true')
    router.push('/')
  }
  // 继续正常导航
  next();
});

export default router 